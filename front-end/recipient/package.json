{"name": "recipient", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "npx prettier --check \"src/**/*.ts\"", "lint:fix": "npx prettier --write \"src/**/*.ts\"", "build:prod": "ng build -c production"}, "private": true, "dependencies": {"@angular-slider/ngx-slider": "^19.0.0", "@angular/animations": "^19.2.7", "@angular/cdk": "^19.2.15", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@foblex/2d": "^1.2.1", "@foblex/flow": "^17.5.3", "@foblex/mediator": "^1.1.3", "@foblex/platform": "^1.0.4", "@foblex/utils": "^1.1.1", "@jsverse/transloco": "^7.6.1", "@ngneat/overview": "6.1.1", "@ngxpert/hot-toast": "^4.1.2", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "apexcharts": "^4.7.0", "date-fns": "^4.1.0", "ng-apexcharts": "~1.15.0", "ng-zorro-antd": "^19.3.1", "ngx-colors": "^3.6.0", "ngx-cookie-service": "^19.1.2", "prettier": "^3.5.3", "rxjs": "~7.8.0", "tailwindcss": "^4.1.4", "tslib": "^2.3.0", "winston": "^3.17.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.8", "@angular/cli": "^19.2.8", "@angular/compiler-cli": "^19.2.0", "@angular/language-service": "^19.2.10", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.21", "daisyui": "^5.0.26", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "typescript": "~5.7.2"}}