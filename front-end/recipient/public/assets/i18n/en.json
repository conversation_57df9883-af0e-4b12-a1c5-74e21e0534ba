{"general": {"search": "Search...", "select": "Select", "buttons": {"save": "Save", "confirm": "Confirm", "cancel": "Cancel", "delete": "Delete", "create": "Create", "update": "Update", "complete": "Complete", "review": "Review", "reset": "Reset", "add": "Add", "overview": "Overview", "accept": "Accept", "reject": "Reject", "edit": "Edit", "done": "Done", "preview": "Preview", "share": "Share", "back_to_portal": "Back to portal", "next": "Next", "previous": "Previous", "mark_complete": "<PERSON><PERSON>", "close": "Close", "change_selection": "Change selection", "mark_as_complete": "Mark as complete", "remove": "Remove", "retry": "Retry", "refresh": "Refresh", "send": "Send", "login": "<PERSON><PERSON>", "register": "Register", "select_all": "Select all", "deselect_all": "Deselect all", "submit": "Submit"}, "selected_all": "All selected", "unknown": "Unknown", "back": "Back", "toasts": {"error": {"title": "Oops, something went wrong!", "description": "It looks like something went wrong on our end. Please try again or contact our support."}, "success": {"title": "Success!", "description": "The action you performed has been successfully completed."}}, "bool": {"true": "Yes", "false": "No"}, "manage": "Manage", "empty_data": "It looks like there is no data available.", "from": "From", "until": "Until", "to": "To", "filter": "Filter", "sort": "Sort by", "settings": "Settings", "request_support": "Request support", "date": "Date", "google": "Google", "microsoft": "Microsoft", "facebook": "Facebook", "linkedin": "LinkedIn", "pinterest": "Pinterest", "tiktok": "TikTok", "instagram": "Instagram", "connected": "Connected", "error": "Error", "preview_mode": "Preview mode. Open in incognito to exit preview mode.", "skipped": "Skipped", "skip": "<PERSON><PERSON>", "synced": "Synced", "connect": "Connect", "failed": "Failed", "failed_data": "Successful", "success": "Success", "pending": "Pending", "connect_your_accounts": "Connect your accounts", "add_another": "Add another one", "skipped_drawer": {"title": "Connection skipped!", "description": "It looks like you have skipped this connection."}, "syncing": "Syncing", "empty_placeholder": "There are no entry's found"}, "pages": {"dashboard": {"title": "Your Performance Report", "recipient": {"title": "Prepared for:"}, "date_ranges": {"this_week": "This week", "last_week": "Last week", "this_quarter": "This quarter", "last_quarter": "Last quarter", "this_year": "This year", "last_year": "Last year", "this_month": "This month", "last_month": "Last month"}, "page": {"widget": {"data-comparison": {"comparison": "vs previous period"}}}, "employee": {"title": "About {{{company}} & Your Account Team", "manager": "Your Account Manager", "message": {"email": "Your email", "message": "Message for {{{firstname}} {{{lastname}}"}}, "settings": "Settings", "filters": {"title": "Filters", "time_period": "Time period", "accuracy": "Accuracy", "segments": {"DAY": "Daily", "WEEK": "Weekly", "MONTH": "Monthly", "QUARTER": "Quarterly", "YEAR": "Yearly"}}}, "whitelabel": {"sidebar": {"title": "Whitelabel settings", "description": "Here you can modify your white-label settings and see the changes in real time.", "defined": "Defined", "back": "Back to languages", "editing": "Currently editing {{{language}}", "tabs": {"text": "Text", "styling": "Styl<PERSON>", "settings": "Settings"}, "language": {"overview": {"add": "Add language"}}, "styling": {"title": "Styl<PERSON>", "information": "The settings will apply to every page and language you have created.", "accent-color": {"title": "Accent color", "color": "Color", "description": "Choose your preferred accent color, such as for buttons, to customize the appearance of your interface."}, "accent-text-color": {"title": "Accent text color", "color": "Color", "description": "Choose your preferred accent text color, such as for buttons, to customize the appearance of your interface."}, "header-background-color": {"title": "Header background color", "color": "Color", "description": "Choose your preferred header background color to customize the appearance of your interface."}, "header-text-color": {"title": "Header text color", "color": "Color", "description": "Choose your preferred header text background color to customize the appearance of your interface."}, "background-color": {"title": "Background color", "color": "Color", "description": "Choose your preferred background color to customize the appearance of your interface."}, "text-color": {"title": "Text color", "color": "Color", "description": "Choose your preferred text color to customize the appearance of your interface."}, "widget-background-color": {"title": "Widget background color", "color": "Color", "description": "Choose your preferred widget background color to customize the appearance of your interface."}, "widget-text-color": {"title": "Widget text color", "color": "Color", "description": "Choose your preferred widget text color to customize the appearance of your interface."}}, "settings": {"title": "Settings", "information": "The settings will apply to every page and language you have created.", "logo": {"title": "Logo", "description": "Upload custom logo to display.", "select_image": "Upload an image", "max_requirements": "PNG, JPG up to 500kb", "toasts": {"max_size": {"title": "Max file size exceeded", "description": "The file you uploaded exceeds the maximum size limit of 500 KB. Please upload a smaller file."}}, "inputs": {"logo": {"title": "Logo", "description": "Upload a custom logo to display on the ConnectPage."}, "size": {"title": "Size", "description": "Customize the logo size to better fit your branding."}}}, "domain": {"title": "Domain", "send_email": "Or send an email to your technical support contact or team.", "inputs": {"url": {"title": "Domain", "description": "Set a custom Domain to enhance the customer experience."}}, "email_dns": {"title": "Send email to domain administrator", "description": "Here you can send an email to your domain administrator with instructions to set the DNS records so the url can be used.", "inputs": {"email": "Email", "name": "Name"}}, "zero_ssl_status": {"title": "SSL certificate status", "description": "Here, the status of your SSL certificate is displayed.", "statuses": {"draft": "Waiting for DNS", "pending_validation": "Waiting for verification", "issued": "Activated"}}, "support": "<a href='https://www.linkmyagency.com/docs/custom-domains/' target='_blank' class='blue'>How to set-up a custom URL</a>"}}}}}}