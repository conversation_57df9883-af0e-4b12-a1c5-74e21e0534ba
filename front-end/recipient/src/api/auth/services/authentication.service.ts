import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { DataResponse } from '@api/support/responses/data.response';
import { paramsToHttpParams } from '@app/helpers/transform-params';
import { ConfigService } from '@app/services/config.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public login(
    slug: string,
    signature: string | null,
  ): Observable<DataResponse<Dashboard>> {
    const params = paramsToHttpParams({ signature });
    return this.httpClient.post<DataResponse<Dashboard>>(
      `${this.endpoint}/api/v1/recipient/auth/login/${slug}`,
      {},
      { params },
    );
  }

  public token(): Observable<void> {
    return this.httpClient.get<void>(
      `${this.endpoint}/recipient/sanctum/csrf-cookie`,
    );
  }
}
