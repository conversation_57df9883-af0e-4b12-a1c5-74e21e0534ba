export interface WhitelabelRequest {
  settings?: WhitelabelSettingsRequest;
  languages?: WhitelabelLanguageRequest[];
}

export interface WhitelabelSettingsRequest {
  domain?: string | null;
  logo?: string | null | File;
  logo_size?: number | null;
  accent_color?: string | null;
  accent_text_color?: string | null;
  header_background_color?: string | null;
  header_text_color?: string | null;
  background_color?: string | null;
  text_color?: string | null;
  widget_background_color?: string | null;
  widget_text_color?: string | null;
}

export interface WhitelabelLanguageRequest {
  language?: string;
}
