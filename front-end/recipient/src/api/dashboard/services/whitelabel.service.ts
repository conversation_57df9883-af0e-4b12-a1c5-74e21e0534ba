import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelRequest } from '@api/dashboard/requests/whitelabel.request';
import { bodyToFormData } from '@helpers/body-to-form-data';
import { WhitelabelEmailDnsRequest } from '@api/dashboard/requests/whitelabel-email-dns.request';

@Injectable({
  providedIn: 'root',
})
export class WhitelabelService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public show(): Observable<DataResponse<Whitelabel>> {
    return this.httpClient.get<DataResponse<Whitelabel>>(
      `${this.endpoint}/api/v1/recipient/whitelabel`,
    );
  }

  public update(body: WhitelabelRequest): Observable<DataResponse<Whitelabel>> {
    return this.httpClient.post<DataResponse<Whitelabel>>(
      `${this.endpoint}/api/v1/recipient/whitelabel`,
      bodyToFormData(body),
    );
  }

  public emailDns(body: WhitelabelEmailDnsRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/recipient/whitelabel/email-dns`,
      body,
    );
  }
}
