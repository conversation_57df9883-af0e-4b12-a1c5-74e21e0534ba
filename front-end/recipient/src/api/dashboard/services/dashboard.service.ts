import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '@services/config.service';
import { DashboardMessageRequest } from '@api/dashboard/requests/dashboard-message.request';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  private readonly endpoint?: string;

  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public message(body: DashboardMessageRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/recipient/dashboard/message`,
      body,
    );
  }
}
