export interface PeriodAnalysisResponse {
  headers: string[];
  categories: PeriodAnalysisCategoryResponse[];
}

export interface PeriodAnalysisCategoryResponse {
  name: string;
  rows: PeriodAnalysisRowResponse[];
}

export interface PeriodAnalysisRowResponse {
  columns: PeriodAnalysisColumnResponse[];
}

export interface PeriodAnalysisColumnResponse {
  value: number | string;
  formatted_value: string;
  prefix: string | null;
  suffix: string | null;
  styling: string | null;
}
