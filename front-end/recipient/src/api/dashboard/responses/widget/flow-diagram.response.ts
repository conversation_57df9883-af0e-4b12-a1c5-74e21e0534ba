export interface FlowDiagramResponse {
  groups: FlowDiagramGroupResponse[];
  connections: FlowDiagramConnectionResponse[];
  entries: FlowDiagramEntryResponse[];
}

export interface FlowDiagramGroupResponse {
  id: string;
  name: string;
  x: number;
  width: number;
}

export interface FlowDiagramConnectionResponse {
  output_id: string;
  input_id: string;
}

export interface FlowDiagramEntryResponse {
  value: number;
  value_formatted: string;
  input_id: string;
  output_id: string;
  name: string;
  position: FlowDiagramEntryPositionResource;
  group_id: string | null;
  comparison_value: number | null;
  comparison_formatted: string | null;
  prefix: string | null;
  suffix: string | null;
}

export interface FlowDiagramEntryPositionResource {
  x: number;
  y: number;
}
