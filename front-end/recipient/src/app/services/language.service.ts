import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Language } from '@interfaces/language.interface';

@Injectable({
  providedIn: 'root',
})
export class LanguageService {
  constructor(private httpClient: HttpClient) {}

  public load(): Observable<Language[]> {
    return this.httpClient.get<Language[]>(`/assets/languages.json`);
  }
}
