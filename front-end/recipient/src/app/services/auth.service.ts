import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of, Subject, tap } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { AuthenticationService } from '@api/auth/services/authentication.service';

export const LOGGED_IN_LOCAL_STORAGE_KEY = 'logged_in';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  public dashboard = signal<Dashboard | null>(null);

  private impersonating: boolean = false;

  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
  ) {}

  public isLoggedIn(): boolean {
    return !!localStorage.getItem(LOGGED_IN_LOCAL_STORAGE_KEY);
  }

  public logout(): void {
    this.dashboard.set(null);

    localStorage.removeItem(LOGGED_IN_LOCAL_STORAGE_KEY);

    this.router.navigate(['/auth']);
  }

  public loadDashboard(
    slug: string,
    signature: string | null = null,
  ): Observable<DataResponse<Dashboard>> {
    if (slug === 'whitelabel') {
      return of({ data: this.dashboard() as Dashboard });
    }

    return this.authenticationService.login(slug, signature).pipe(
      tap((response) => {
        this.dashboard.set(response.data);
      }),
    );
  }

  public isImpersonating(): boolean {
    return this.impersonating;
  }

  public setImpersonating(value: boolean): void {
    this.impersonating = value;
  }
}
