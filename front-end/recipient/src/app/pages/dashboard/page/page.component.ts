import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnChanges,
  OnInit,
  signal,
  SimpleChanges,
} from '@angular/core';
import { Page } from '@api/dashboard/models/page.interface';
import { AuthService } from '@app/services/auth.service';
import { toNumber } from '@jsverse/transloco';
import { WidgetComponent } from './widget/widget.component';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';
import { NgStyle } from '@angular/common';
import { InformationIconComponent } from '@components/information-icon/information-icon.component';

@Component({
  selector: 'app-page',
  templateUrl: './page.component.html',
  imports: [WidgetComponent, NgStyle, InformationIconComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PageComponent implements OnChanges, OnInit {
  @Input('id') pageId!: string;
  public page = signal<Page | null>(null);
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private authService = inject(AuthService);
  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public ngOnChanges(changes: SimpleChanges): void {
    this.page.set(
      this.authService
        .dashboard()
        ?.pages?.filter((page) => page.id === toNumber(this.pageId))
        .at(0) ?? null,
    );
  }
}
