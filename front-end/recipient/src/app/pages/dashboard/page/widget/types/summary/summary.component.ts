import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  signal,
} from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { SummaryResponse } from '@api/dashboard/responses/widget/summary.response';
import { NgStyle } from '@angular/common';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-summary',
  imports: [NgStyle],
  templateUrl: './summary.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SummaryComponent {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: SummaryResponse;
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }
}
