<div
  *transloco="let t; read: 'pages.dashboard.page.widget.data-comparison'"
  [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
  class="w-full p-4 rounded bg-white shadow h-full flex items-start justify-between"
>
  <div class="space-y-4">
    <div class="space-y-1">
      <p class="text-sm text-eaglo-secondary-gray">{{ widget.name }}</p>
      <p class="font-medium">
        @if(widget.settings && widget.settings['prefix']) {
          <span>{{ widget.settings['prefix'] }}</span>
        }
        <span>{{ statistics?.value_formatted }}</span>
        @if(widget.settings && widget.settings['suffix']) {
          <span>{{ widget.settings['suffix'] }}</span>
        }
      </p>
    </div>

    @if(comparisonPercentage(); as percentage) {
      <p class="space-x-1 text-sm">
        <span
        [ngClass]="{
          '!text-red-500': percentage < 0,
          '!text-green-500': percentage > 0
        }"
        class="text-eaglo-secondary-gray"
        >{{ percentage }}%</span>
        <span class="text-eaglo-secondary-gray">{{ t('comparison') }}</span>
      </p>
    }
  </div>
  @if(widget.settings && widget.settings['icon']) {
    <div
      [ngStyle]="{
        'background-color': whitelabel()?.accent_color ? 'color-mix(in oklab, ' + whitelabel()?.accent_color + ' 10%, transparent)' : null
      }"
      class="h-8 w-8 bg-eaglo-blue/10 rounded-full flex items-center justify-center"
    >
      <i
        [ngStyle]="{
          color: whitelabel()?.accent_color
        }"
        [classList]="widget.settings['icon'] + ' --whitelabel-text text-eaglo-blue'"></i>
    </div>
  }
</div>
