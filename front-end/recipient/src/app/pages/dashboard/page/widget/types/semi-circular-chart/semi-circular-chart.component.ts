import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { SemiCircularChartResponse } from '@api/dashboard/responses/widget/semi-circular-chart.response';
import { NgApexchartsModule } from 'ng-apexcharts';
import { NgStyle } from '@angular/common';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-radial-chart',
  imports: [NgApexchartsModule, NgStyle],
  templateUrl: './semi-circular-chart.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SemiCircularChartComponent implements OnInit {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: SemiCircularChartResponse;

  public plotOptions = signal<ApexPlotOptions>({});
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    const settings = Object.keys(this.widget.settings ?? {});

    if (settings.includes('angle')) {
      this.plotOptions.set({
        radialBar: {
          startAngle: this.widget.settings['angle']['start'],
          endAngle: this.widget.settings['angle']['end'],
        },
      });
    }

    this.whitelabel = this.whitelabelState.whitelabel;
  }
}
