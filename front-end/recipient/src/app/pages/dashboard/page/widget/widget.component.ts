import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  Input,
  OnChanges,
  OnInit,
  signal,
  SimpleChanges,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { Widget } from '@api/dashboard/models/widget.interface';
import { DataComparisonResponse } from '@api/dashboard/responses/widget/data-comparison.response';
import { WidgetService } from '@api/dashboard/services/widget.service';
import { WidgetDataType } from '@api/dashboard/support/enums/widget-data-type.enum';
import { catchError, tap } from 'rxjs';
import { DataComparisonComponent } from './types/data-comparison/data-comparison.component';
import { SummaryResponse } from '@api/dashboard/responses/widget/summary.response';
import { SummaryComponent } from './types/summary/summary.component';
import { ColumnChartComponent } from './types/column-chart/column-chart.component';
import { ChartResponse } from '@api/dashboard/responses/widget/chart.response';
import { TableResponse } from '@api/dashboard/responses/widget/table.response';
import { TableComponent } from './types/table/table.component';
import { format } from 'date-fns';
import { SemiCircularChartComponent } from './types/semi-circular-chart/semi-circular-chart.component';
import { SemiCircularChartResponse } from '@api/dashboard/responses/widget/semi-circular-chart.response';
import { LineChartComponent } from '@pages/dashboard/page/widget/types/line-chart/line-chart.component';
import { PeriodAnalysisResponse } from '@api/dashboard/responses/widget/period-analysis.response';
import { PeriodAnalysisComponent } from '@pages/dashboard/page/widget/types/period-analysis/period-analysis.component';
import { FlowDiagramComponent } from '@pages/dashboard/page/widget/types/flow-diagram/flow-diagram.component';
import { FlowDiagramResponse } from '@api/dashboard/responses/widget/flow-diagram.response';
import { PieChartResponse } from '@api/dashboard/responses/widget/pie-chart.response';
import { PieChartComponent } from '@pages/dashboard/page/widget/types/pie-chart/pie-chart.component';
import { DashboardState } from '@states/dashboard.state';
import { AuthService } from '@services/auth.service';
import {
  generateChartStatistics,
  generateDataComparison,
  generatePieChartStatistics,
  generateRadialStatistics,
} from '@helpers/generate-dummy-statistics';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';
import { NgStyle } from '@angular/common';

@Component({
  selector: 'dashboard-page-widget',
  imports: [
    DataComparisonComponent,
    SummaryComponent,
    ColumnChartComponent,
    TableComponent,
    SemiCircularChartComponent,
    LineChartComponent,
    PeriodAnalysisComponent,
    FlowDiagramComponent,
    PieChartComponent,
    NgStyle,
  ],
  templateUrl: './widget.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WidgetComponent implements OnChanges, OnInit {
  @Input({ required: true }) widget!: Widget;
  public statistics = signal<
    | DataComparisonResponse
    | SummaryResponse
    | ChartResponse
    | TableResponse
    | SemiCircularChartResponse
    | PeriodAnalysisResponse
    | FlowDiagramResponse
    | PieChartResponse
    | null
  >(null);
  public loading = signal<boolean>(false);
  public hasError = signal<boolean>(false);
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  public dataType = WidgetDataType;

  private widgetService = inject(WidgetService);
  private destroyRef = inject(DestroyRef);
  private dashboardState = inject(DashboardState);
  private authService = inject(AuthService);
  private whitelabelState = inject(WhitelabelState);

  constructor() {
    toObservable(this.dashboardState.filters)
      .pipe(
        tap(() => {
          this.loadStatistics();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public ngOnChanges(changes: SimpleChanges): void {
    this.loadStatistics();
  }

  public asDataComparison(): DataComparisonResponse {
    return this.statistics() as DataComparisonResponse;
  }

  public asSummary(): SummaryResponse {
    return this.statistics() as SummaryResponse;
  }

  public asChart(): ChartResponse {
    return this.statistics() as ChartResponse;
  }

  public asTable(): TableResponse {
    return this.statistics() as TableResponse;
  }

  public asRadialChart(): SemiCircularChartResponse {
    return this.statistics() as SemiCircularChartResponse;
  }

  public asPeriodAnalysis(): PeriodAnalysisResponse {
    return this.statistics() as PeriodAnalysisResponse;
  }

  public asFlowDiagram(): FlowDiagramResponse {
    return this.statistics() as FlowDiagramResponse;
  }

  public asPieChart(): PieChartResponse {
    return this.statistics() as PieChartResponse;
  }

  private loadStatistics(): void {
    if (this.authService.dashboard()?.slug === 'whitelabel') {
      this.setDummyStatistics();
      return;
    }

    if (!this.widget || this.loading()) {
      return;
    }

    this.loading.set(true);
    this.hasError.set(false);

    this.widgetService
      .statistics(this.widget, {
        start_date: format(
          this.dashboardState.filters().date_range?.at(0) ?? new Date(),
          'yyyy-MM-dd',
        ),
        end_date: format(
          this.dashboardState.filters().date_range?.at(1) ?? new Date(),
          'yyyy-MM-dd',
        ),
      })
      .pipe(
        tap((response) => {
          this.statistics.set(response.data);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.hasError.set(true);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private setDummyStatistics(): void {
    let statistics = null;

    switch (this.widget.data_type) {
      case WidgetDataType.SEMI_CIRCULAR_CHART:
        statistics = generateRadialStatistics();
        break;
      case WidgetDataType.DATA_COMPARISON:
        statistics = generateDataComparison();
        break;
      case WidgetDataType.LINE_CHART:
      case WidgetDataType.COLUMN_CHART:
        statistics = generateChartStatistics();
        break;
      case WidgetDataType.PIE_CHART:
        statistics = generatePieChartStatistics();
        break;
    }

    this.statistics.set(statistics);
  }
}
