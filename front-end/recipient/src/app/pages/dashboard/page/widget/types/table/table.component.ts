import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  signal,
} from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { TableResponse } from '@api/dashboard/responses/widget/table.response';
import { NgStyle } from '@angular/common';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-table',
  imports: [NgStyle],
  templateUrl: './table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableComponent {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: TableResponse;
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }
}
