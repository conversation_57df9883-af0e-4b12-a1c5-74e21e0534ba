import { Component, inject, Input, OnInit, signal } from '@angular/core';
import { ChartComponent } from 'ng-apexcharts';
import { Widget } from '@api/dashboard/models/widget.interface';
import { PieChartResponse } from '@api/dashboard/responses/widget/pie-chart.response';
import { NgStyle } from '@angular/common';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-pie-chart',
  imports: [ChartComponent, NgStyle],
  templateUrl: './pie-chart.component.html',
})
export class PieChartComponent implements OnInit {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: PieChartResponse;

  public series = signal<number[]>([]);
  public labels = signal<string[]>([]);
  public colors = signal<(string | null)[]>([]);
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.setColors();
    this.setSeries();
    this.setLabels();
    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public dataLabel = (val: number, opts: any): string => {
    return this.statistics.series.at(opts.seriesIndex)?.formatted_value ?? '';
  };

  private setSeries(): void {
    this.series.set(this.statistics.series.map((series) => series.value));
  }

  private setLabels(): void {
    this.labels.set(this.statistics.series.map((series) => series.label));
  }

  private setColors(): void {
    const allHaveColor =
      this.statistics.series.filter((series) => !!series.color).length ===
      this.statistics.series.length;

    this.colors.set(
      allHaveColor ? this.statistics.series.map((series) => series.color) : [],
    );
  }
}
