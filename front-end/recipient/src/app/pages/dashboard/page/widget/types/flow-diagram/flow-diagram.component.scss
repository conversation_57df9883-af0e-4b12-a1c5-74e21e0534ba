.f-flow {
  min-height: 600px;
  border-radius: 8px;
  padding: 16px;
  cursor: grab;

  // CSS custom properties for connection markers
  --connection-gradient-1: #235FE7; // Start marker color (circle)
  --connection-gradient-2: #235FE7; // End marker color (arrow)
}

.f-group {
  padding: 10px 16px;
  color: #1e293b;
  text-align: center;
  background: #235FE7;
  border-radius: 6px;
  border: none;
  width: auto;
  min-width: 160px;
  box-shadow: 0 1px 3px rgba(35, 95, 231, 0.12);
  position: relative;

  p {
    color: white;
    font-weight: 500;
    font-size: 13px;
    margin: 0;
  }

  &.f-selected {
    border: 1px solid #fbbf24;
    box-shadow: 0 2px 8px rgba(35, 95, 231, 0.2);
  }
}

.f-node {
  padding: 12px 16px;
  color: #1e293b;
  text-align: center;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  width: 140px;
  min-height: 70px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #235FE7;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-color: #235FE7;
  }

  &.f-selected {
    border-color: #235FE7;
    box-shadow: 0 2px 12px rgba(35, 95, 231, 0.15);
  }
}

.f-drag-handle {
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

// Node content styling
.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  .node-title {
    font-weight: 500;
    font-size: 11px;
    color: #64748b;
    margin: 0;
    line-height: 1.2;
  }

  .node-value {
    font-size: 14px;
    font-weight: 600;
    color: #235FE7;
    margin: 0;
    line-height: 1;
  }

  .node-comparison {
    font-size: 9px;
    padding: 1px 6px;
    border-radius: 8px;
    font-weight: 500;
    margin: 0;

    &.positive {
      background-color: #dcfce7;
      color: #166534;
    }

    &.negative {
      background-color: #fef2f2;
      color: #dc2626;
    }

    &.neutral {
      background-color: #f1f5f9;
      color: #64748b;
    }
  }
}

// Group content styling
.group-content {
  .group-title {
    font-weight: 500;
    font-size: 13px;
    color: white;
    margin: 0;
  }
}

::ng-deep {
  .f-connection {
    .f-connection-drag-handle {
      fill: transparent;
      // By default, this element has a black fill and is used to detect the start of dragging (e.g., onmousedown).
      // We make it transparent to avoid visual clutter, while keeping it functional.
    }

    .f-connection-selection {
      stroke-width: 8;
      stroke: transparent;
      // This is a pseudo-connection (a copy of the main path) used to make it easier to select the connection.
      // It's slightly thicker than the actual path (which is often only 1px), making it easier to interact with.
      // It remains invisible to avoid affecting visual clarity but stays active for user interaction.
    }

    .f-connection-path {
      stroke: #cbd5e1;
      stroke-width: 2;
      stroke-linecap: round;
      transition: all 0.2s ease;
      cursor: pointer;
    }

    &:hover {
      .f-connection-path {
        stroke: #235FE7;
        stroke-width: 2.5;
      }
    }

    &.f-selected {
      .f-connection-path {
        stroke: #235FE7;
        stroke-width: 2.5;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .f-flow {
    padding: 12px;
    min-height: 600px;
  }

  .f-node {
    width: 120px;
    padding: 10px 12px;
    min-height: 60px;

    .node-content {
      gap: 3px;

      .node-title {
        font-size: 10px;
      }

      .node-value {
        font-size: 12px;
      }

      .node-comparison {
        font-size: 8px;
        padding: 1px 4px;
      }
    }
  }

  .f-group {
    min-width: 140px;
    padding: 8px 12px;

    .group-content .group-title {
      font-size: 12px;
    }
  }
}

// Loading state (if needed in the future)
.f-flow.loading {
  .f-node, .f-group {
    opacity: 0.6;
    pointer-events: none;
  }

  ::ng-deep .f-connection-path {
    opacity: 0.4;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .f-node, .f-group, ::ng-deep .f-connection-path {
    transition: none;
  }
}

// Connection styles
::ng-deep {
  .f-connection {
    .f-connection-drag-handle {
      fill: transparent;
    }

    .f-connection-selection {
      stroke-width: 8;
      stroke: transparent;
    }

    .f-connection-path {
      stroke: #cbd5e1;
      stroke-width: 2;
      stroke-linecap: round;
      transition: all 0.2s ease;
    }

    &:hover {
      .f-connection-path {
        stroke: #235FE7;
        stroke-width: 2.5;
      }
    }

    &.f-selected {
      .f-connection-path {
        stroke: #235FE7;
        stroke-width: 2.5;
      }
    }
  }
}
