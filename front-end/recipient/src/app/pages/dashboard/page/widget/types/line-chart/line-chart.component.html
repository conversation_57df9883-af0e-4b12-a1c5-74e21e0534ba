@if(statistics) {
  <div
    [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
    class="w-full p-4 rounded bg-white shadow h-full"
  >
    @if(statistics.name) {
      <p class="text-sm text-eaglo-secondary-gray">{{ statistics.name }}</p>
    }
    <apx-chart
      [chart]="{
        type: 'line',
        height: 350,
        zoom: {
          enabled: false
        },
        toolbar: {
          show: false
        }
      }"
      [series]="statistics.series"
      [dataLabels]="{
        enabled: false
      }"
      [tooltip]="apexToolTip"
      [xaxis]="xAxis"
      [annotations]="{
        points: anomalies()
      }"
    ></apx-chart>
  </div>
}
