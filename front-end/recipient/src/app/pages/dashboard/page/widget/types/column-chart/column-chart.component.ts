import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnChanges,
  OnInit,
  signal,
  SimpleChanges,
} from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { ChartResponse } from '@api/dashboard/responses/widget/chart.response';
import { WidgetAccuracy } from '@api/dashboard/support/enums/widget-accuracy.enum';
import { NgApexchartsModule } from 'ng-apexcharts';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';
import { NgStyle } from '@angular/common';

@Component({
  selector: 'widget-column-chart',
  imports: [NgApexchartsModule, NgStyle],
  templateUrl: './column-chart.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ColumnChartComponent implements OnChanges, OnInit {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: ChartResponse;
  public whitelabel = signal<Partial<Whitelabel> | null>(null);
  public xAxis: ApexXAxis = this.getXAxis();
  public apexToolTip: ApexTooltip = {
    y: {
      formatter: function (val, opts) {
        const dataPoint =
          opts.w.config.series[opts.seriesIndex].data[opts.dataPointIndex];
        return (
          `${dataPoint.prefix ?? ''}${dataPoint.value_formatted}${dataPoint.suffix ?? ''}` ||
          `${val}`
        );
      },
    },
  };

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (!('statistics' in changes)) {
      return;
    }

    this.xAxis = this.getXAxis();
  }

  private getXAxis(): ApexXAxis {
    return {
      type: 'datetime',
      labels: {
        format:
          this.statistics?.accuracy === WidgetAccuracy.MONTH
            ? 'MM-yyyy'
            : 'dd-MM',
      },
    };
  }
}
