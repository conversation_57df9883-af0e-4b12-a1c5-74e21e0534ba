<div
  [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
  class="w-full p-4 rounded bg-white shadow h-full"
>
  <p>{{ widget.name }}</p>

  <!-- SVG Semi-circular progress chart -->
  <div class="relative w-full flex justify-center items-center pb-2">
    <div class="relative w-full mx-auto">
      <!-- Main Progress Chart (75% example) -->
      <svg viewBox="0 0 200 120" class="h-auto max-w-[23rem] mx-auto">
        <!-- Background arc -->
        <path d="M 20 100 A 80 80 0 0 1 180 100"
              fill="none"
              stroke="#e5e7eb"
              stroke-width="12"
              stroke-linecap="square"/>

        <!-- Progress arc (dynamic based on percentage) -->
        @if(statistics.percentage > 0) {
          <path d="M 20 100 A 80 80 0 0 1 180 100"
                fill="none"
                stroke="#3b82f6"
                stroke-width="12"
                stroke-linecap="square"
                [attr.stroke-dasharray]="statistics.percentage + ' 251.3'"
                class="transition-all duration-1000 ease-out"/>
        }

        <!-- Halfway marker line -->
        @if(statistics.needs_halfway_marker) {
          <line x1="100" y1="15" x2="100" y2="25"
                stroke="#6b7280"
                stroke-width="2"
                stroke-linecap="round"/>
        }

        <!-- Center text -->
        <text x="100" y="85" text-anchor="middle" class="text-lg font-medium fill-gray-700 space-x-0.5">
          @if(statistics.prefix) {
            {{ statistics.prefix }}
          }
          {{ statistics.value_formatted }}
          @if(statistics.suffix) {
            {{ statistics.suffix }}
          }
        </text>

        <text x="10" y="120" text-anchor="start" class="text-xs fill-gray-700 space-x-0.5">
          @if(statistics.prefix) {
            {{ statistics.prefix }}
          }
          {{ statistics.min }}
          @if(statistics.suffix) {
            {{ statistics.suffix }}
          }
        </text>

        <text x="195" y="120" text-anchor="end" class="text-xs fill-gray-700 space-x-0.5">
          @if(statistics.prefix) {
            {{ statistics.prefix }}
          }
          {{ statistics.max }}
          @if(statistics.suffix) {
            {{ statistics.suffix }}
          }
        </text>
      </svg>
    </div>
  </div>
<!--  <div class="relative">-->
<!--    <div class="overflow-hidden w-full" [ngStyle]="getChartContainerStyle()">-->
<!--      <apx-chart-->
<!--        [chart]="{-->
<!--          type: 'radialBar',-->
<!--          height: 300,-->
<!--          width: '100%'-->
<!--        }"-->
<!--        [series]="series()"-->
<!--        [plotOptions]="{-->
<!--          radialBar: {-->
<!--            startAngle: plotOptions().radialBar?.startAngle ?? -180,-->
<!--            endAngle: plotOptions().radialBar?.endAngle ?? 180,-->
<!--            hollow: {-->
<!--              size: '60%'-->
<!--            },-->
<!--            track: {-->
<!--              strokeWidth: '100%'-->
<!--            },-->
<!--            dataLabels: {-->
<!--             show: false-->
<!--            }-->
<!--          }-->
<!--        }"-->
<!--        [responsive]="[-->
<!--          {-->
<!--            breakpoint: 480,-->
<!--            options: {-->
<!--              chart: {-->
<!--                width: '100%'-->
<!--              }-->
<!--            }-->
<!--          }-->
<!--        ]"-->
<!--        [annotations]="{-->
<!--        points: [{-->
<!--          x: 100,-->
<!--          y: 100,-->
<!--          marker: {-->
<!--            size: 8,-->
<!--            fillColor: '#ff4444',-->
<!--            strokeColor: 'blue',-->
<!--            strokeWidth: 2,-->
<!--          },-->
<!--          label: {-->
<!--            text: '50%',-->
<!--            offsetY: -10,-->
<!--            style: {-->
<!--              color: '#ff4444',-->
<!--              fontSize: '12px'-->
<!--            }-->
<!--          }-->
<!--        }]-->
<!--        }"-->
<!--      ></apx-chart>-->
<!--    </div>-->


<!--    &lt;!&ndash; Positioned labels at the stroke endpoints &ndash;&gt;-->
<!--    @if(statistics.min) {-->
<!--      <div class="absolute text-center top-2/3 left-1/4">-->
<!--        <p class="text-sm font-medium whitespace-nowrap">-->
<!--          @if(statistics.prefix) {-->
<!--            <span>{{ statistics.prefix }}</span>-->
<!--          }-->
<!--          <span>{{ statistics.min}}</span>-->
<!--          @if(statistics.suffix) {-->
<!--            <span>{{ statistics.suffix }}</span>-->
<!--          }-->
<!--        </p>-->
<!--      </div>-->
<!--    }-->
<!--    @if(statistics.max) {-->
<!--      <div class="absolute text-center top-2/3 right-1/5">-->
<!--        <p class="text-sm font-medium whitespace-nowrap">-->
<!--          @if(statistics.prefix) {-->
<!--            <span>{{ statistics.prefix }}</span>-->
<!--          }-->
<!--          <span>{{ statistics.max}}</span>-->
<!--          @if(statistics.suffix) {-->
<!--            <span>{{ statistics.suffix }}</span>-->
<!--          }-->
<!--        </p>-->
<!--      </div>-->
<!--    }-->
<!--  </div>-->


</div>
