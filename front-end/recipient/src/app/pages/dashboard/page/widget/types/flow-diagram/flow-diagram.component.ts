import { Component, inject, Input, signal } from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { EFMarkerType, FFlowComponent, FFlowModule } from '@foblex/flow';
import {
  FlowDiagramEntryResponse,
  FlowDiagramResponse,
} from '@api/dashboard/responses/widget/flow-diagram.response';
import { DecimalPipe, NgStyle } from '@angular/common';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-flow-diagram',
  imports: [FFlowComponent, FFlowModule, DecimalPipe, NgStyle],
  templateUrl: './flow-diagram.component.html',
  styleUrl: './flow-diagram.component.scss',
})
export class FlowDiagramComponent {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: FlowDiagramResponse;
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  public readonly eMarkerType = EFMarkerType;

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public getComparisonPercentage(entry: FlowDiagramEntryResponse): number {
    if (!entry.comparison_value) {
      return 0;
    }
    return (entry.comparison_value / entry.value) * 100;
  }
}
