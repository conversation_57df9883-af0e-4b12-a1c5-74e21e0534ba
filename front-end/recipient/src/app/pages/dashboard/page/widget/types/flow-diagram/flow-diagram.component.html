<div
  [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
  class="bg-white w-full h-full p-4 rounded-lg shadow-sm border border-gray-100"
>
  <f-flow fDraggable>
    <f-canvas fZoom [fZoomStep]="0.05" [scale]="0.9">


      @for(group of statistics.groups; track group.id; let index = $index) {
        <div fGroup [fGroupId]="group.id"
             [fGroupPosition]="{ x: group.x, y: 0 }" [fGroupSize]="{ width: group.width, height: 50 }">
          <div class="group-content">
            <p class="group-title">{{ group.name }}</p>
          </div>
        </div>
      }

      @for(connection of statistics.connections; track $index) {
        <f-connection [fOutputId]="connection.output_id" [fInputId]="connection.input_id" fBehavior="floating" fType="straight">
          <svg viewBox="0 0 10 10" fMarker [type]="eMarkerType.START" [height]="8" [width]="8" [refX]="4" [refY]="4">
            <circle cx="4" cy="4" r="2.5" stroke="white" stroke-width="1" fill="var(--connection-gradient-1)"></circle>
          </svg>
          <svg viewBox="0 0 700 700" fMarker [type]="eMarkerType.END" [height]="6" [width]="6" [refX]="5" [refY]="3">
            <path fill="var(--connection-gradient-2)" stroke="white" stroke-width="20" d="M0,0L700,350L0,700L150,350z"/>
          </svg>
        </f-connection>
      }

      @for(entry of statistics.entries; track $index) {
        <div
          fNode
          fNodeOutput
          fNodeInput
          [fNodePosition]="{ x: entry.position.x, y: entry.position.y }"
          [fOutputId]="entry.output_id"
          [fInputId]="entry.input_id"
          [fNodeParentId]="entry.group_id"
        >
          <div class="node-content">
            <p class="node-title">{{ entry.name }}</p>

            @if(entry.value_formatted) {
              <p class="node-value">
                @if(entry.prefix) {
                  <span>{{ entry.prefix }}</span>
                }
                <span>{{ entry.value_formatted }}</span>
                @if(entry.suffix) {
                  <span>{{ entry.suffix }}</span>
                }
              </p>
            }

            @if(entry.comparison_formatted && entry.comparison_value !== null) {
              <p class="node-comparison"
                 [class.positive]="entry.comparison_value < entry.value"
                 [class.negative]="entry.comparison_value > entry.value"
                 [class.neutral]="entry.comparison_value === entry.value">
                  @if(entry.prefix) {
                    <span>{{ entry.prefix }}</span>
                  }
                  <span>{{ entry.comparison_formatted }}</span>
                  @if(entry.suffix) {
                    <span>{{ entry.suffix }}</span>
                  }
              </p>
            }
          </div>
        </div>
      }
    </f-canvas>
  </f-flow>

</div>
