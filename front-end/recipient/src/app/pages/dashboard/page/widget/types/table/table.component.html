@if(statistics) {
  <div
    [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
    class="bg-white rounded shadow overflow-hidden"
  >
    <div class="overflow-x-auto">
      <table class="min-w-full table-auto">
        <thead class="border-b border-gray-200">
          <tr class="text-xs text-gray-500">
            @for(header of statistics.headers; track $index) {
              <th class="px-6 py-3 text-left font-medium">{{ header }}</th>
            }
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          @for(row of statistics.rows; track $index) {
            <tr>
              @for(entry of row; track $index; let first = $first) {
                <td [class.font-semibold]="first" class="px-6 py-4 text-left text-gray-900 whitespace-nowrap">{{ entry }}</td>
              }
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
}
