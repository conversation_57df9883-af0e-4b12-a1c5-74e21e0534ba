@if(loading()) {
  <div
    [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
    class="h-40 w-full flex items-center justify-center bg-white rounded shadow"
  >
    <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-2xl"></i>
  </div>
} @else if(hasError()) {
  <div
    [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
    class="h-40 w-full flex items-center justify-center bg-white rounded shadow text-eag"
  >
    <i class="fa-regular fa-triangle-exclamation text-2xl text-orange-500"></i>
  </div>
} @else {
  @if(widget.data_type === dataType.DATA_COMPARISON) {
    <widget-data-comparison [widget]="widget" [statistics]="asDataComparison()"></widget-data-comparison>
  }

  @if(widget.data_type === dataType.SUMMARY) {
    <widget-summary [widget]="widget" [statistics]="asSummary()"></widget-summary>
  }

  @if(widget.data_type === dataType.COLUMN_CHART) {
    <widget-column-chart [widget]="widget" [statistics]="asChart()"></widget-column-chart>
  }

  @if(widget.data_type === dataType.TABLE) {
    <widget-table [widget]="widget" [statistics]="asTable()"></widget-table>
  }

  @if(widget.data_type === dataType.SEMI_CIRCULAR_CHART) {
    <widget-radial-chart [widget]="widget" [statistics]="asRadialChart()"></widget-radial-chart>
  }

  @if(widget.data_type === dataType.LINE_CHART) {
    <widget-line-chart [widget]="widget" [statistics]="asChart()"></widget-line-chart>
  }

  @if(widget.data_type === dataType.PERIOD_ANALYSIS) {
    <widget-period-analysis [widget]="widget" [statistics]="asPeriodAnalysis()"></widget-period-analysis>
  }

  @if(widget.data_type === dataType.FLOW_DIAGRAM) {
    <widget-flow-diagram [widget]="widget" [statistics]="asFlowDiagram()"></widget-flow-diagram>
  }

  @if(widget.data_type === dataType.PIE_CHART) {
    <widget-pie-chart [widget]="widget" [statistics]="asPieChart()"></widget-pie-chart>
  }
}
