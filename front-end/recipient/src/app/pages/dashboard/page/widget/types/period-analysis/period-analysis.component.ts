import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  Input,
  OnChanges,
  OnInit,
  signal,
  SimpleChanges,
} from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import {
  PeriodAnalysisCategoryResponse,
  PeriodAnalysisResponse,
} from '@api/dashboard/responses/widget/period-analysis.response';
import { NgClass, NgStyle } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SelectComponent } from '@components/form-inputs/select/select.component';
import { SelectOption } from '@interfaces/select-option.interface';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-period-analysis',
  imports: [Ng<PERSON>lass, NgStyle, ReactiveFormsModule, SelectComponent],
  templateUrl: './period-analysis.component.html',
})
export class PeriodAnalysisComponent implements OnInit, OnChanges {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: PeriodAnalysisResponse;
  public category = signal<PeriodAnalysisCategoryResponse | null>(null);
  public categories = signal<SelectOption<string>[]>([]);
  public control: FormControl<SelectOption<string> | null> = new FormControl(
    null,
  );
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private destroyRef = inject(DestroyRef);
  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.listenToControl();

    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (!('statistics' in changes)) {
      return;
    }

    this.setCategories();
    this.setInitialCategory();
  }

  private setCategories(): void {
    this.categories.set(
      this.statistics.categories.map(
        (category) =>
          ({
            label: category.name,
            value: category.name,
          }) as SelectOption<string>,
      ),
    );
  }

  private setInitialCategory(): void {
    const category = this.statistics.categories.at(0);

    if (!category) {
      return;
    }

    this.control.setValue({
      label: category.name,
      value: category.name,
    } as SelectOption<string>);
    this.category.set(category);
  }

  private listenToControl(): void {
    this.control.valueChanges
      .pipe(
        tap((value) => {
          const category = this.statistics.categories
            .filter((category) => category.name === value?.value)
            .at(0);

          if (!category) {
            return;
          }

          this.category.set(category);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
