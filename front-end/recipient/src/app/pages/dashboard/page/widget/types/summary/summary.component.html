<div
  [ngStyle]="{
      'background-color': whitelabel()?.accent_color,
      color: whitelabel()?.accent_text_color
    }"
  class="w-full bg-eaglo-blue rounded shadow space-y-4 p-4 text-white"
>
  <p class="font-medium text-2xl">{{ widget.name }}</p>
  <p [innerHTML]="statistics.text" class="text-sm font-light"></p>

  <div class="flex items-center space-x-8">
    @for(field of statistics.fields; track $index) {
      <div>
        <p class="text-sm font-light">{{ field.title }}</p>
        <p class="font-semibold">
          @if(field.prefix) {
            <span>{{ field.prefix }}</span>
          }
          <span>{{ field.value_formatted }}</span>
          @if(field.suffix) {
            <span>{{ field.suffix }}</span>
          }
        </p>
      </div>
    }
  </div>
</div>
