import { <PERSON><PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnInit,
  signal,
  WritableSignal,
} from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { DataComparisonResponse } from '@api/dashboard/responses/widget/data-comparison.response';
import { TranslocoDirective } from '@jsverse/transloco';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-data-comparison',
  imports: [TranslocoDirective, NgClass, NgStyle],
  templateUrl: './data-comparison.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataComparisonComponent implements OnInit {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true })
  statistics!: DataComparisonResponse | null;
  public comparisonPercentage = signal<number | null>(null);
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.setComparisonPercentage();

    this.whitelabel = this.whitelabelState.whitelabel;
  }

  private setComparisonPercentage(): void {
    if (!this.statistics?.comparison) {
      return;
    }

    const percentage =
      ((this.statistics.value - this.statistics.comparison) /
        this.statistics.comparison) *
      100;

    this.comparisonPercentage.set(Math.round(percentage * 100) / 100);
  }
}
