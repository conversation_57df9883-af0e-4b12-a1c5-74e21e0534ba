import {
  Component,
  inject,
  Input,
  OnChanges,
  signal,
  SimpleChanges,
} from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { ChartResponse } from '@api/dashboard/responses/widget/chart.response';
import { WidgetAccuracy } from '@api/dashboard/support/enums/widget-accuracy.enum';
import { ChartComponent, PointAnnotations } from 'ng-apexcharts';
import { parse } from 'date-fns';
import { NgStyle } from '@angular/common';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';

@Component({
  selector: 'widget-line-chart',
  imports: [ChartComponent, NgStyle],
  templateUrl: './line-chart.component.html',
})
export class LineChartComponent implements OnChanges {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: ChartResponse;

  public apexToolTip: ApexTooltip = {
    y: {
      formatter: function (val, opts) {
        const dataPoint =
          opts.w.config.series[opts.seriesIndex].data[opts.dataPointIndex];
        return (
          `${dataPoint.prefix ?? ''}${dataPoint.value_formatted}${dataPoint.suffix ?? ''}` ||
          `${val}`
        );
      },
    },
  };

  public anomalies = signal<PointAnnotations[]>([]);

  public xAxis: ApexXAxis = this.getXAxis();

  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (!('statistics' in changes)) {
      return;
    }

    this.xAxis = this.getXAxis();
    this.anomalies.set(this.getAnomalies());
  }

  private getXAxis(): ApexXAxis {
    return {
      type: 'datetime',
      labels: {
        format:
          this.statistics?.accuracy === WidgetAccuracy.MONTH
            ? 'MM-yyyy'
            : 'dd-MM',
      },
    };
  }

  private getAnomalies(): PointAnnotations[] {
    return this.statistics.anomalies.map(
      (anomaly): PointAnnotations => ({
        x: parse(anomaly.date, 'yyyy-MM-dd', new Date()).getTime(),
        y: anomaly.value,
        marker: {
          size: 8,
          fillColor: anomaly.color,
          strokeWidth: 1,
        },
        seriesIndex: anomaly.series_index,
      }),
    );
  }
}
