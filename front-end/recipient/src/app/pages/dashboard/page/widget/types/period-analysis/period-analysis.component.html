@if(statistics) {
  <div
    [ngStyle]="{
      'background-color': whitelabel()?.widget_background_color,
      color: whitelabel()?.widget_text_color
    }"
    class="bg-white p-4 rounded shadow space-y-6"
  >
    <div class="flex justify-end">
      <form-select [formControl]="control" [data]="categories()" class="w-64"></form-select>
    </div>
    <div class="rounded overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full table-auto">
          <thead class="border-b border-gray-200">
            <tr class="text-xs text-gray-500">
              @for(header of statistics.headers; track $index) {
                <th class="px-6 py-3 text-left font-medium">{{ header }}</th>
              }
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
          @if(category(); as category) {
            @for(row of category.rows; track $index) {
              <tr>
                @for(entry of row.columns; track $index; let first = $first) {
                  <td
                    [ngClass]="{
                      'font-semibold': first
                    }"
                    [style]="entry.styling"
                    class="px-6 py-4 text-left text-gray-900 whitespace-nowrap"
                  >
                    @if(entry.prefix) {
                      <span>{{ entry.prefix }}</span>
                    }
                    <span>{{ entry.formatted_value }}</span>
                    @if(entry.suffix) {
                      <span>{{ entry.suffix }}</span>
                    }
                  </td>
                }
              </tr>
            }
          }
          </tbody>
        </table>
      </div>
    </div>
  </div>
}
