@if(page(); as page) {
  <div class="space-y-6">
    @for(section of page.sections; track $index) {
      <div class="flex items-center space-x-2">
        <h2
          [ngStyle]="{
          color: whitelabel()?.text_color
          }"
          class="text-xl font-medium"
        >
          {{ section.name }}
        </h2>
        @if(section.tooltip) {
          <information-icon [text]="section.tooltip" class="block mt-1.5"></information-icon>
        }
      </div>

      <div [style.gridTemplateColumns]="'repeat(' + section.grid_columns + ', minmax(0, 1fr))'" class="grid gap-4">
        @for(widget of section.widgets; track $index) {
          @defer(on viewport) {
            <dashboard-page-widget
              [widget]="widget"
              [style.gridColumn]="'span ' + widget.col_span + ' / span ' + widget.col_span"
            ></dashboard-page-widget>
          } @placeholder {
            <div class="h-12 w-12">
              <i class="fa-duotone fa-solid fa-spinner-third animate-spin"></i>
            </div>
          }
        }
      </div>
    }
  </div>
}
