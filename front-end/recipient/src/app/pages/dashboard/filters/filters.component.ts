import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { debounceTime, distinctUntilChanged, Subject, tap } from 'rxjs';
import { ModalComponent } from '@components/modal/modal.component';
import {
  NzDatePickerComponent,
  NzRangePickerComponent,
} from 'ng-zorro-antd/date-picker';
import {
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
  subDays,
  subMonths,
  subQuarters,
  subWeeks,
  subYears,
} from 'date-fns';
import { DashboardState } from '@states/dashboard.state';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { WidgetAccuracy } from '@api/dashboard/support/enums/widget-accuracy.enum';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

interface Form {
  date_range: FormControl<Date[] | null>;
  widget_accuracy: FormControl<WidgetAccuracy>;
}

@Component({
  selector: 'app-filters',
  imports: [
    ModalComponent,
    NzDatePickerComponent,
    NzRangePickerComponent,
    FormsModule,
    TranslocoDirective,
    ReactiveFormsModule,
  ],
  templateUrl: './filters.component.html',
  styleUrl: './filters.component.scss',
})
export class FiltersComponent implements OnInit {
  public close$: Subject<void> = new Subject();
  public ranges: { [key: string]: Date[] } = {};
  public form: FormGroup<Form> = new FormGroup<Form>({
    date_range: new FormControl(null),
    widget_accuracy: new FormControl(WidgetAccuracy.MONTH, {
      nonNullable: true,
    }),
  });

  public readonly accuracy = WidgetAccuracy;

  private dashboardState = inject(DashboardState);
  private translocoService = inject(TranslocoService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.setRanges();

    this.form.patchValue({
      date_range: this.dashboardState.filters().date_range,
    });

    this.listenForForm();
  }

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }

  private listenForForm(): void {
    this.form.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap((value) => {}),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private async setRanges(): Promise<void> {
    const today = new Date();
    const t = await this.translocoService.load('en').toPromise();

    if (!t) {
      return;
    }

    this.ranges = {
      [t['pages']['dashboard']['date_ranges']['this_week']]: [
        startOfWeek(today),
        endOfWeek(today),
      ],
      [t['pages']['dashboard']['date_ranges']['last_week']]: [
        startOfWeek(subWeeks(today, 1)),
        endOfWeek(subWeeks(today, 1)),
      ],
      [t['pages']['dashboard']['date_ranges']['this_month']]: [
        startOfMonth(today),
        endOfMonth(today),
      ],
      [t['pages']['dashboard']['date_ranges']['last_month']]: [
        startOfMonth(subMonths(today, 1)),
        endOfMonth(subMonths(today, 1)),
      ],
      [t['pages']['dashboard']['date_ranges']['this_quarter']]: [
        startOfQuarter(today),
        endOfQuarter(today),
      ],
      [t['pages']['dashboard']['date_ranges']['last_quarter']]: [
        startOfQuarter(subQuarters(today, 1)),
        endOfQuarter(subQuarters(today, 1)),
      ],
      [t['pages']['dashboard']['date_ranges']['this_year']]: [
        startOfYear(today),
        endOfYear(today),
      ],
      [t['pages']['dashboard']['date_ranges']['last_year']]: [
        startOfYear(subYears(today, 1)),
        endOfYear(subYears(today, 1)),
      ],
    };
  }
}
