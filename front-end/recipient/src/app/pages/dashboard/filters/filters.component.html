<components-modal *transloco="let t; read: 'pages.dashboard.filters'" (close$)="close()">
  <div class="space-y-4">
    <h2 class="text-xl font-medium">{{ t('title') }}</h2>

    <div class="form-group-new">
      <label>{{ t('time_period') }}</label>
      <nz-range-picker [formControl]="form.controls.date_range" [nzAllowClear]="false" [nzRanges]="ranges" class="block w-full"></nz-range-picker>
    </div>

    <div class="form-group-new">
      <label>{{ t('accuracy') }}</label>
      <div class="accuracy-container">
        <div>
          <input [formControl]="form.controls.widget_accuracy" [id]="accuracy.DAY" type="radio">
          <label [for]="accuracy.DAY">{{ t('segments.' + accuracy.DAY) }}</label>
        </div>
        <div>
          <input [formControl]="form.controls.widget_accuracy" [id]="accuracy.WEEK" type="radio">
          <label [for]="accuracy.WEEK">{{ t('segments.' + accuracy.WEEK) }}</label>
        </div>
        <div>
          <input [formControl]="form.controls.widget_accuracy" [id]="accuracy.MONTH" type="radio">
          <label [for]="accuracy.MONTH">{{ t('segments.' + accuracy.MONTH) }}</label>
        </div>
        <div>
          <input [formControl]="form.controls.widget_accuracy" [id]="accuracy.QUARTER" type="radio">
          <label [for]="accuracy.QUARTER">{{ t('segments.' + accuracy.QUARTER) }}</label>
        </div>
        <div>
          <input [formControl]="form.controls.widget_accuracy" [id]="accuracy.YEAR" type="radio">
          <label [for]="accuracy.YEAR">{{ t('segments.' + accuracy.YEAR) }}</label>
        </div>
      </div>
    </div>
  </div>
</components-modal>
