import { DestroyRef, inject, Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { FiltersComponent } from '@pages/dashboard/filters/filters.component';
import { take, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Injectable({
  providedIn: 'root',
})
export class FiltersService {
  private modalService = inject(ModalService);
  private destroyRef = inject(DestroyRef);

  public show(): void {
    const modal = this.modalService.attach(FiltersComponent);

    modal.componentRef.instance.close$
      .pipe(
        tap(() => modal.overlayRef.detach()),
        take(1),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
