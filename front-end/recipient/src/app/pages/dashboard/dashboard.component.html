@if(dashboard(); as dashboard) {
  <ng-container *transloco="let t; read: 'pages.dashboard'">
    <div
         [ngStyle]="{
          'background-color': whitelabel()?.header_background_color,
          color: whitelabel()?.header_text_color
         }"
         class="bg-white shadow sticky w-full z-[99] top-0"
    >
      <div class="max-w-6xl mx-auto pt-4 space-y-8">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <img
              [src]="whitelabel()?.logo ?? 'assets/images/logo.png'"
              [style]="{
                width: (whitelabel()?.logo ? (whitelabel()?.logo_size ?? 112) : 112) + 'px'
              }"
              class=" bg-contain max-w-[450px] h-auto"
            >
            <h2 class="text-2xl font-semibold">{{ t('title') }}</h2>
          </div>

          <div class="flex items-center space-x-1">
            <p class="text-eaglo-secondary-gray text-sm">{{ t('recipient.title') }} </p>
            <h2 class="font-medium text-lg">{{ dashboard.recipient?.company }}</h2>
          </div>
        </div>

        <div class="flex justify-between">
          <nav class="flex space-x-4 pb-2">
            @for(page of dashboard.pages; track $index) {
              <a
                [routerLink]="[page.id]"
                [queryParams]="activatedRoute.snapshot.queryParams"
                [relativeTo]="activatedRoute"
                [style.background-color]="rla.isActive ? (whitelabel()?.accent_color + ' !important') : null"
                [style.color]="rla.isActive ? (whitelabel()?.accent_text_color + ' !important') : null"
                #rla="routerLinkActive"
                routerLinkActive="!text-white !bg-eaglo-black"
                class="py-1 px-2 transition-colors duration-200 font-medium rounded hover:bg-eaglo-gray/20"
              >
              {{ page.name }}
              </a>
            }
          </nav>

          <div class="flex space-x-4 pb-2">
            @if(dashboard.settings) {
              <a [href]="dashboard.settings" class="btn --outline !text-eaglo-gray --small">
                <i class="fa-regular fa-gear"></i>
                <p>{{ t('settings') }}</p>
              </a>
            }
            <button (click)="openFilters()" class="btn --outline --small" type="button">
              <i class="fa-regular fa-filter"></i>
              <p>{{ t('filters.title') }}</p>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      [ngStyle]="{
        'background-color': whitelabel()?.background_color
      }"
      class="min-h-full"
    >
      <div
        class="py-8 space-y-8 max-w-6xl mx-auto">
        <router-outlet></router-outlet>

        <div
          [ngStyle]="{
          'background-color': whitelabel()?.widget_background_color,
          color: whitelabel()?.widget_text_color
        }"
          class="w-full bg-white rounded shadow mt-8 p-4 space-y-4"
        >
          <h2 class="text-xl font-medium">{{ t('employee.title', {company: dashboard.account?.name }) }}</h2>
          <div class="flex space-x-8">
            <div [innerHTML]="dashboard.account?.about | safeHtml" class="w-2/3 inner-html-container"></div>
            <div
              [ngStyle]="{
                'background-color': whitelabel()?.accent_color ? 'color-mix(in oklab, ' + whitelabel()?.accent_color + ' 10%, transparent)' : null
              }"
              class="flex flex-col items-center p-8 rounded bg-eaglo-blue/10 w-1/3"
            >
              @if(dashboard.user?.image_url) {
                <img [src]="dashboard.user?.image_url" class="bg-white rounded p-1 h-auto w-full bg-contain">
              } @else {
                <div class="w-full h-48 bg-white rounded p-1">
                  <div class="flex justify-center items-center h-full"><i class="fa-regular fa-user text-6xl"></i></div>
                </div>
              }
              <p class="text-lg font-medium pt-4">{{ dashboard.user?.firstname }} {{ dashboard.user?.lastname }}</p>
              <p
                [ngStyle]="{
                  'color': whitelabel()?.accent_color
                }"
                class="text-eaglo-blue"
              >{{ t('employee.manager') }}</p>

              <div class="form-group-new pt-4">
                <label>{{ t('employee.message.email') }}</label>
                <input [formControl]="form.controls.email">
              </div>

              <div class="form-group-new pt-4">
                <label>{{ t('employee.message.message', {firstname: dashboard.user?.firstname, lastname: dashboard.user?.lastname}) }}</label>
                <textarea [formControl]="form.controls.message"></textarea>
              </div>

              <button
                (click)="submit()"
                [ngStyle]="{
                  'background-color': whitelabel()?.accent_color,
                  'color': whitelabel()?.accent_text_color,
                }"
                [class.--disabled]="form.invalid"
                [class.--loading]="submitting()"
                class="btn w-full mt-2">{{ 'general.buttons.submit' | transloco }}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
}
