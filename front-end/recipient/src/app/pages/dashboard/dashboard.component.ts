import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterLinkActive,
  RouterOutlet,
} from '@angular/router';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { AuthService } from '@app/services/auth.service';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import {
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
  subDays,
  subMonths,
  subQuarters,
  subWeeks,
  subYears,
} from 'date-fns';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { DashboardState } from '@states/dashboard.state';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';
import { NgStyle } from '@angular/common';
import { ToastService } from '@services/toast.service';
import { DashboardService } from '@api/dashboard/services/dashboard.service';
import { DashboardMessageRequest } from '@api/dashboard/requests/dashboard-message.request';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SafeHtmlPipe } from '@pipes/safe-html.pipe';
import { FiltersService } from '@pages/dashboard/filters/filters.service';

interface Form {
  email: FormControl<string | null>;
  message: FormControl<string | null>;
}

@Component({
  selector: 'dashboard',
  imports: [
    RouterOutlet,
    TranslocoDirective,
    RouterLink,
    RouterLinkActive,
    NzDatePickerModule,
    FormsModule,
    NgStyle,
    ReactiveFormsModule,
    TranslocoPipe,
    SafeHtmlPipe,
  ],
  templateUrl: './dashboard.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardComponent implements OnInit {
  public dashboard = signal<Dashboard | null>(null);
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  public submitting = signal<boolean>(false);

  public form: FormGroup<Form> = new FormGroup<Form>({
    email: new FormControl(null, [Validators.required, Validators.email]),
    message: new FormControl(null, [Validators.required]),
  });

  private authService = inject(AuthService);
  private router = inject(Router);
  public activatedRoute = inject(ActivatedRoute);
  private translocoService = inject(TranslocoService);
  private dashboardState = inject(DashboardState);
  private whitelabelState = inject(WhitelabelState);
  private destroyRef = inject(DestroyRef);
  private toastService = inject(ToastService);
  private dashboardService = inject(DashboardService);
  private filtersService = inject(FiltersService);

  public ngOnInit(): void {
    this.dashboard = this.authService.dashboard;
    this.whitelabel = this.whitelabelState.whitelabel;

    const page = this.dashboard()?.pages?.at(0);

    this.openFilters();

    if (!page) {
      return;
    }

    this.router.navigate([page.id], {
      relativeTo: this.activatedRoute,
      queryParams: this.activatedRoute.snapshot.queryParams,
    });
  }

  public submit(): void {
    if (this.submitting() || this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.submitting.set(true);

    this.dashboardService
      .message(this.form.value as DashboardMessageRequest)
      .pipe(
        tap(() => {
          this.submitting.set(false);
          this.toastService.success();
        }),
        catchError((err) => {
          this.submitting.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public openFilters(): void {
    this.filtersService.show();
  }
}
