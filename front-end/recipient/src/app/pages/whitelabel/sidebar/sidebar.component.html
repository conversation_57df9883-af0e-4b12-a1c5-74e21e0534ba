<ng-container *transloco="let t; read: 'pages.whitelabel.sidebar'">
<!--  <div class="h-full w-full bg-gray-100 min-w-[31rem] relative flex flex-col">-->
    <div class="h-full w-full bg-gray-100 min-w-[31rem] relative flex flex-col">
      @if(activeTab() === tab.SELECT || activeTab() === tab.ADD) {
        <ng-container *ngTemplateOutlet="language"></ng-container>
      } @else {
        <ng-container *ngTemplateOutlet="settings"></ng-container>
      }
    </div>

<!--  </div>-->

  <ng-template #language>
    <div class="bg-eaglo-blue px-6 py-4 space-y-1">
      <h2 class="font-semibold text-2xl text-white">{{ t('title') }}</h2>
      <p class="text-white w-3/4">{{ t('description') }}</p>
    </div>

    <div class="flex-1 overflow-hidden">
      @if(activeTab() === tab.SELECT) {
        <whitelabel-sidebar-language-overview (selectLanguage$)="setActiveLanguage($event)" (addLanguage$)="selectTab(tab.ADD)"></whitelabel-sidebar-language-overview>
      }

      @if(activeTab() === tab.ADD) {
        <whitelabel-sidebar-language-add (selectLanguage$)="setActiveLanguage($event)" (cancel$)="selectTab(tab.SELECT)"></whitelabel-sidebar-language-add>
      }
    </div>
  </ng-template>

  <ng-template #settings>
    <div class="bg-eaglo-blue px-6 py-4 flex justify-between items-center">
      <button (click)="back()" type="button" class="space-x-2 cursor-pointer">
        <i class="fa-regular fa-chevron-left text-white"></i>
        <span class="text-white">{{ t('back') }}</span>
      </button>
      @if(activeLanguage(); as language) {
        <p class="text-white text-sm">({{ t('editing', {language: languages()[language]}) }})</p>
      }
    </div>

    <div class="grid grid-cols-3 bg-white">
<!--      <p (click)="selectTab(tab.TEXT)" [class.active]="activeTab() === tab.TEXT" class="item">{{ t('tabs.text') }}</p>-->
      <p (click)="selectTab(tab.STYLING)"
         [ngClass]="{
          'border-b-4 bg-eaglo-blue/5 text-eaglo-blue': activeTab() === tab.STYLING
         }"
         class="border-eaglo-blue hover:text-eaglo-blue cursor-pointer text-center py-4"
      >
        {{ t('tabs.styling') }}
      </p>
      <p
        (click)="selectTab(tab.SETTINGS)"
        [ngClass]="{
          'border-b-4 bg-eaglo-blue/5 text-eaglo-blue': activeTab() === tab.SETTINGS
         }"
        class="border-eaglo-blue hover:text-eaglo-blue cursor-pointer text-center py-4"
      >
        {{ t('tabs.settings') }}
      </p>
    </div>
    <div class="flex-1 overflow-y-auto">
<!--      @if(activeTab() === tab.TEXT) {-->
<!--&lt;!&ndash;        <whitelabel-sidebar-text [language]="activeLanguage"></whitelabel-sidebar-text>&ndash;&gt;-->
<!--      }-->

      @if (activeTab() === tab.STYLING) {
        <whitelabel-sidebar-tabs-styling></whitelabel-sidebar-tabs-styling>
      }

      @if(activeTab() === tab.SETTINGS) {
        <whitelabel-sidebar-settings></whitelabel-sidebar-settings>
      }
    </div>
  </ng-template>
</ng-container>
