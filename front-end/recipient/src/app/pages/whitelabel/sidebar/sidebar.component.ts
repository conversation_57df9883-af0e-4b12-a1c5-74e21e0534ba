import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { ConfigService } from '@services/config.service';
import { LanguageService } from '@services/language.service';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AddComponent } from '@pages/whitelabel/sidebar/language/add/add.component';
import { OverviewComponent } from '@pages/whitelabel/sidebar/language/overview/overview.component';
import { WhitelabelState } from '@states/whitelabel.state';
import { StylingComponent } from '@pages/whitelabel/sidebar/tabs/styling/styling.component';
import { SettingsComponent } from '@pages/whitelabel/sidebar/tabs/settings/settings.component';

export enum Tab {
  // TEXT = 'TEXT',
  STYLING = 'STYLING',
  SETTINGS = 'SETTINGS',
  SELECT = 'SELECT',
  ADD = 'ADD',
}

@Component({
  selector: 'whitelabel-sidebar',
  imports: [
    NgTemplateOutlet,
    TranslocoDirective,
    AddComponent,
    OverviewComponent,
    StylingComponent,
    NgClass,
    SettingsComponent,
  ],
  templateUrl: './sidebar.component.html',
})
export class SidebarComponent implements OnInit {
  public activeTab = signal<Tab>(Tab.SELECT);
  public loadedLanguages = signal<string[]>(['en']);
  public languages = signal<{ [key: string]: string }>({});
  public activeLanguage = signal<string | null>(null);

  public tab = Tab;

  private translocoService = inject(TranslocoService);
  private destroyRef = inject(DestroyRef);
  private languageService = inject(LanguageService);
  private configService = inject(ConfigService);
  private whitelabelService = inject(WhitelabelState);

  public ngOnInit(): void {
    this.activeLanguage = this.whitelabelService.activeLanguage;
    this.whitelabelService.portalUrl.set(
      `${
        this.configService.config?.environment.portal_domain ?? null
      }/#/settings`,
    );
    this.loadLanguages();

    if (this.configService.config?.dev_options?.active_tab) {
      this.activeTab.set(this.configService.config.dev_options.active_tab);
    }
  }

  public selectTab(tab: Tab): void {
    this.activeTab.set(tab);
  }

  public triggerScrollEvent(): void {
    const event = new Event('scroll');

    window.dispatchEvent(event);
  }

  public setActiveLanguage(lang: string): void {
    if (this.loadedLanguages().includes(lang)) {
      this.whitelabelService.activeLanguage.set(lang);
      this.activeTab.set(Tab.STYLING);
      return;
    }

    this.translocoService
      .load(lang)
      .pipe(
        tap(() => {
          this.whitelabelService.activeLanguage.set(lang);
          this.loadedLanguages().push(lang);
          this.activeTab.set(Tab.STYLING);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public back(): void {
    this.activeTab.set(Tab.SELECT);
  }

  private loadLanguages(): void {
    this.languageService
      .load()
      .pipe(
        tap((languages) => {
          const mapped: { [key: string]: string } = {};

          languages.forEach((language) => {
            mapped[language.code] = language.language;
          });

          this.languages.set(mapped);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
