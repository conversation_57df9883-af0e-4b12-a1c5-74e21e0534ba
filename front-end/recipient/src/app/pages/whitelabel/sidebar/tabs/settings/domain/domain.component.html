<div *transloco="let t; read: 'pages.whitelabel.sidebar.settings.domain'" class="w-full bg-white rounded border-gray-300 border py-4 space-y-4">
  <div (click)="toggleOpen()" class="flex justify-between items-center cursor-pointer px-4">
    <h2 class="text-lg select-none">{{ t('title') }}</h2>
    <div [class.rotate-180]="open()" class="transition ease-in-out"><i class="fa-regular fa-chevron-down"></i></div>
  </div>

  @if(open()) {
    <div class="w-full h-px bg-gray-300"></div>

    <div class="space-y-4 px-4">
      <div class="form-group-new">
        <label class="flex items-center space-x-2">
          <span>{{ t('inputs.url.title') }}</span>
          <information-icon [text]="t('inputs.url.description')" [customIcon]="true" width="w-64">
            <div customIcon class="bg-eaglo-blue h-4 w-4 rounded-full flex items-center justify-center cursor-pointer">
              <i class="fa-regular fa-info text-white text-[0.65rem]"></i>
            </div>
          </information-icon>
        </label>
        <input [formControl]="form.controls.domain" [placeholder]="recipientDomain()">
      </div>

      @if(whitelabel()?.zero_ssl_status) {
        <div class="space-y-2">
          <p class="flex items-center space-x-2">
            <span>{{ t('zero_ssl_status.title') }}</span>
            <information-icon [text]="t('zero_ssl_status.description')" [customIcon]="true" width="w-64">
              <div customIcon class="bg-eaglo-blue h-4 w-4 rounded-full flex items-center justify-center cursor-pointer">
                <i class="fa-regular fa-info text-white text-[0.65rem]"></i>
              </div>
            </information-icon>
          </p>
          <p [class.!text-green-500]="whitelabel()?.zero_ssl_status === sslStatus.ISSUED" class="text-orange-400">{{ t('zero_ssl_status.statuses.' + (whitelabel()?.zero_ssl_status ?? '')) }}</p>
        </div>
      }

      <div [innerHTML]="t('support')" class="text-sm"></div>

      @if(whitelabel()?.domain) {
        <button (click)="openEmailDns()" class="link text-left text-gray-400 text-sm">{{ t('send_email') }}</button>
      }
    </div>

  }
</div>
