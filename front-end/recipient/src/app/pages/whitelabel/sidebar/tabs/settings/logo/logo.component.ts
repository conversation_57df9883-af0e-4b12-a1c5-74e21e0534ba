import { Component, inject, Input, OnInit, signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { SettingsForm } from '@pages/whitelabel/sidebar/tabs/settings/settings.component';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';
import { ToastService } from '@services/toast.service';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { InformationIconComponent } from '@components/information-icon/information-icon.component';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

@Component({
  selector: 'whitelabel-sidebar-settings-logo',
  imports: [
    TranslocoDirective,
    InformationIconComponent,
    TranslocoPipe,
    NgxSliderModule,
  ],
  templateUrl: './logo.component.html',
  styleUrl: './logo.component.scss',
})
export class LogoComponent implements OnInit {
  @Input({ required: true }) form!: FormGroup<SettingsForm>;
  public open = signal<boolean>(false);
  public whitelabel = signal<Partial<Whitelabel> | null>(null);

  private whitelabelState = inject(WhitelabelState);
  private toastService = inject(ToastService);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
  }

  public toggleOpen(): void {
    this.open.update((value) => !value);
  }

  public imageChanged(event: any): void {
    const files = event.target?.files as File[];

    if (files.length === 0) {
      return;
    }

    if (files[0].size > 500 * 1024) {
      this.toastService.error(
        'pages.whitelabel.sidebar.settings.logo.toasts.max_size.title',
        'pages.whitelabel.sidebar.settings.logo.toasts.max_size.description',
      );
      return;
    }

    const selectedImage = files[0];

    const reader = new FileReader();

    reader.onload = (event: any) => {
      const existing = this.whitelabel() ?? {};

      existing.logo = event.target.result;

      this.whitelabelState.whitelabel.update((whitelabel) =>
        whitelabel ? { ...whitelabel, ...existing } : { ...existing },
      );
    };

    reader.readAsDataURL(selectedImage);

    this.form?.controls.logo.setValue(selectedImage);
  }

  public clearImages(event: any): void {
    event.target.value = null;
  }

  public sizeChange(event: number): void {
    const existing = this.whitelabel() ?? {};

    const size = (112 / 100) * event;

    existing.logo_size = size;

    this.whitelabelState.whitelabel.update((whitelabel) =>
      whitelabel ? { ...whitelabel, ...existing } : { ...existing },
    );

    this.form?.controls.logo_size.setValue(size, { emitEvent: false });
  }

  public delete(): void {
    const whitelabel = this.whitelabel();
    if (!whitelabel) {
      return;
    }

    whitelabel.logo = null;
    whitelabel.logo_size = null;
    this.whitelabel.set(whitelabel);
    this.form?.controls.logo.setValue(null);
    this.form?.controls.logo_size.setValue(null);
  }
}
