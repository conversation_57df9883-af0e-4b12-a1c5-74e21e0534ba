<div *transloco="let t; read: 'pages.whitelabel.sidebar.settings.logo'" class="w-full bg-white rounded border-gray-300 border py-4 space-y-4">
  <div (click)="toggleOpen()" class="flex justify-between items-center cursor-pointer px-4">
    <h2 class="text-lg select-none">{{ t('title') }}</h2>
    <div [class.rotate-180]="open()" class="transition ease-in-out"><i class="fa-regular fa-chevron-down"></i></div>
  </div>

  @if(open() && form) {
    <div class="w-full h-px bg-gray-300"></div>

    <div class="space-y-4 px-4">
      <p class="flex items-center space-x-2">
        <span>{{ t('inputs.logo.title') }}</span>
        <information-icon [text]="t('inputs.logo.description')" [customIcon]="true" width="w-64">
          <div customIcon class="bg-eaglo-blue h-4 w-4 rounded-full flex items-center justify-center cursor-pointer">
            <i class="fa-regular fa-info text-white text-[0.65rem]"></i>
          </div>
        </information-icon>
      </p>
      <label for="logo" class="w-full h-64 border-4 border-linkmyagency-gray-800 border-dashed rounded-lg block mt-4 bg-white cursor-pointer p-2">
        @if(!whitelabel()?.logo) {
          <div class="flex flex-col justify-center items-center w-full h-full space-y-4">
            <i class="fa-light fa-image text-6xl"></i>
            <div class="text-center">
              <p>{{ t('select_image') }}</p>
              <p class="text-sm text-linkmyagency-gray-500">{{ t('max_requirements') }}</p>
            </div>
          </div>
        }

        @if(whitelabel()?.logo) {
          <div class="relative group h-full w-full">
            <div class="absolute h-full w-full bg-gray-300/60 flex items-center justify-center group-hover:opacity-100 opacity-0 transition">
              <i class="fa-light fa-pen text-4xl"></i>
            </div>
            <div class="h-full w-full bg-center bg-no-repeat bg-contain" [style.background-image]="'url(' + whitelabel()?.logo +')'"></div>
          </div>
        }
      </label>

      <input (change)="imageChanged($event)" (click)="clearImages($event)" id="logo" type="file" accept="image/png, image/jpg" class="invisible absolute">

      @if(whitelabel()?.logo) {
        <p class="flex items-center space-x-2">
          <span>{{ t('inputs.size.title') }}</span>
          <information-icon [text]="t('inputs.size.description')" [customIcon]="true" width="w-64">
            <div customIcon class="bg-eaglo-blue h-4 w-4 rounded-full flex items-center justify-center cursor-pointer">
              <i class="fa-regular fa-info text-white text-[0.65rem]"></i>
            </div>
          </information-icon>
        </p>

        <div class="logo-slider pb-8">
          <ngx-slider
            [value]="(whitelabel()?.logo_size ?? 112)/112 * 100"
            [options]="{
            floor: 50,
            ceil: 150,
            showTicksValues: true,
            tickStep:25,
            tickValueStep: 25
          }"
            (valueChange)="sizeChange($event)"
          ></ngx-slider>
        </div>
      }

      @if(whitelabel()?.logo) {
        <div (click)="delete()" class="flex justify-end">
          <button class="btn --danger">{{ 'general.buttons.delete' | transloco }}</button>
        </div>
      }
    </div>
  }
</div>
