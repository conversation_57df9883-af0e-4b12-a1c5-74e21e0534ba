import { Component, inject, Input, signal } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SettingsForm } from '@pages/whitelabel/sidebar/tabs/settings/settings.component';
import { ZeroSslStatus } from '@api/dashboard/enums/zero-ssl-status.enum';
import { ConfigService } from '@services/config.service';
import { WhitelabelState } from '@states/whitelabel.state';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { TranslocoDirective } from '@jsverse/transloco';
import { InformationIconComponent } from '@components/information-icon/information-icon.component';
import { EmailDnsService } from '@pages/whitelabel/sidebar/tabs/settings/domain/email-dns/email-dns.service';

@Component({
  selector: 'whitelabel-sidebar-settings-domain',
  imports: [TranslocoDirective, InformationIconComponent, ReactiveFormsModule],
  templateUrl: './domain.component.html',
})
export class DomainComponent {
  @Input({ required: true }) form!: FormGroup<SettingsForm>;
  public whitelabel = signal<Partial<Whitelabel> | null>(null);
  public open = signal<boolean>(true);
  public recipientDomain = signal<string | null>(null);

  public readonly sslStatus = ZeroSslStatus;

  private configService = inject(ConfigService);
  private whitelabelState = inject(WhitelabelState);
  private emailDnsService = inject(EmailDnsService);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelState.whitelabel;
    this.recipientDomain.set(
      this.configService.config?.environment.recipient_domain ?? null,
    );
  }

  public toggleOpen(): void {
    this.open.update((open) => !open);
  }

  public openEmailDns(): void {
    this.emailDnsService.show();
  }
}
