import { inject, Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { EmailDnsComponent } from '@pages/whitelabel/sidebar/tabs/settings/domain/email-dns/email-dns.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class EmailDnsService {
  private modalService = inject(ModalService);

  public show(): void {
    const modal = this.modalService.attach(EmailDnsComponent);

    modal.componentRef.instance.close$
      .pipe(
        tap(() => modal.overlayRef.detach()),
        take(1),
      )
      .subscribe();
  }
}
