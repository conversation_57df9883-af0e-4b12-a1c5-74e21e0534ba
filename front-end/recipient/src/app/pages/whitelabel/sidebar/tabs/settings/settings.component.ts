import { Component, inject, OnInit, signal } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { InformationIconComponent } from '@components/information-icon/information-icon.component';
import { LogoComponent } from '@pages/whitelabel/sidebar/tabs/settings/logo/logo.component';
import { WhitelabelState } from '@states/whitelabel.state';
import { DomainComponent } from '@pages/whitelabel/sidebar/tabs/settings/domain/domain.component';

export interface SettingsForm {
  logo: FormControl<File | null>;
  logo_size: FormControl<number | null>;
  domain: FormControl<string | null>;
}

@Component({
  selector: 'whitelabel-sidebar-settings',
  imports: [
    TranslocoDirective,
    InformationIconComponent,
    LogoComponent,
    TranslocoPipe,
    DomainComponent,
  ],
  templateUrl: './settings.component.html',
})
export class SettingsComponent implements OnInit {
  public form: FormGroup<SettingsForm> = new FormGroup<SettingsForm>({
    logo: new FormControl(null),
    logo_size: new FormControl(null),
    domain: new FormControl(null),
  });
  public loading = signal<boolean>(false);

  public whitelabelState = inject(WhitelabelState);

  public ngOnInit(): void {
    this.initForm();
  }

  public submit(): void {
    if (this.loading()) {
      return;
    }

    this.whitelabelState.submit(this.loading, {
      logo: this.form.controls.logo.value,
      domain: this.form.controls.domain.value,
    });
  }

  private initForm(): void {
    const whitelabel = this.whitelabelState.whitelabel();

    if (!whitelabel) {
      return;
    }

    this.form.patchValue({
      logo_size: whitelabel.logo_size,
      domain: whitelabel.domain,
    });
  }
}
