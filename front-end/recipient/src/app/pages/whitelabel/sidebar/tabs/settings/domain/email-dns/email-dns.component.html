<components-modal *transloco="let t; read: 'pages.whitelabel.sidebar.settings.domain.email_dns'" (close$)="close()">
  <div class="space-y-4">
    <div class="space-y-1">
      <h2 class="text-xl font-medium">{{ t('title') }}</h2>
      <p>{{ t('description') }}</p>
    </div>
    <div class="space-y-1">
      <div class="form-group-new">
        <label>{{ t('inputs.name') }}</label>
        <input [formControl]="form.controls.name">
      </div>

      <div class="form-group-new">
        <label>{{ t('inputs.email') }}</label>
        <input [formControl]="form.controls.email">
      </div>
    </div>
    <div class="flex space-x-2 items-center justify-between">
      <button (click)="close()" class="btn --outline">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.--disabled]="form?.invalid || loading()" class="btn">{{ 'general.buttons.send' | transloco }}</button>
    </div>
  </div>
</components-modal>
