import { Component, inject, signal } from '@angular/core';
import { catchError, Subject, tap } from 'rxjs';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { WhitelabelService } from '@api/dashboard/services/whitelabel.service';
import { ToastService } from '@services/toast.service';
import { WhitelabelEmailDnsRequest } from '@api/dashboard/requests/whitelabel-email-dns.request';
import { ModalComponent } from '@components/modal/modal.component';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';

interface Form {
  email: FormControl<string | null>;
  name: FormControl<string | null>;
}

@Component({
  selector: 'app-email-dns',
  imports: [
    ModalComponent,
    TranslocoDirective,
    ReactiveFormsModule,
    TranslocoPipe,
  ],
  templateUrl: './email-dns.component.html',
})
export class EmailDnsComponent {
  public close$: Subject<void> = new Subject();
  public form: FormGroup<Form> = new FormGroup<Form>({
    email: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
  });
  public loading = signal<boolean>(false);

  private whitelabelService = inject(WhitelabelService);
  private toastService = inject(ToastService);

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }

  public submit(): void {
    if (this.loading() || this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.loading.set(true);

    const body = this.form.value as WhitelabelEmailDnsRequest;

    this.whitelabelService
      .emailDns(body)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.close();
          this.toastService.success();
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
      )
      .subscribe();
  }
}
