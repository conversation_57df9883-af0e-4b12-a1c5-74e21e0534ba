<div class="space-y-4 h-full flex flex-col pt-4">
  <div *transloco="let t; read: 'pages.whitelabel.sidebar.settings'" class="space-y-4 flex-1 overflow-y-auto px-6">
    <div class="flex items-center space-x-2">
      <h2 class="text-xl font-medium">{{ t('title') }}</h2>
      <information-icon [text]="t('information')" [customIcon]="true" width="w-64">
        <div customIcon class="bg-eaglo-blue h-4 w-4 rounded-full flex items-center justify-center cursor-pointer">
          <i class="fa-regular fa-info text-white text-[0.65rem]"></i>
        </div>
      </information-icon>
    </div>

    <whitelabel-sidebar-settings-logo [form]="form" class="block"></whitelabel-sidebar-settings-logo>
    <whitelabel-sidebar-settings-domain [form]="form" class="block"></whitelabel-sidebar-settings-domain>
<!--    <whitelabel-sidebar-settings-url [form]="form" [whitelabel]="whitelabel" class="block"></whitelabel-sidebar-settings-url>-->
<!--    <whitelabel-sidebar-settings-meta-title [form]="form" class="block"></whitelabel-sidebar-settings-meta-title>-->
<!--    <whitelabel-sidebar-settings-email-address [form]="form" class="block"></whitelabel-sidebar-settings-email-address>-->


<!--    @if(language !== 'en') {-->
<!--      <button (click)="delete()" class="btn &#45;&#45;danger w-full">{{ t('delete_button') }}</button>-->
<!--    }-->
  </div>
  <div class="flex justify-between items-center px-6 bg-white py-4">
    <a [href]="whitelabelState.portalUrl()" class="btn --outline">{{ 'general.buttons.back_to_portal' | transloco }}</a>
    <button (click)="submit()" [class.--loading]="loading()" class="btn --blue">{{ 'general.buttons.save' | transloco }}</button>
  </div>
</div>
