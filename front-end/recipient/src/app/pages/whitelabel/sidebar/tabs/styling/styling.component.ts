import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { WhitelabelState } from '@states/whitelabel.state';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { InformationIconComponent } from '@components/information-icon/information-icon.component';
import { NgxColorsModule } from 'ngx-colors';
import { KeyValuePipe, NgTemplateOutlet } from '@angular/common';
import {
  WhitelabelRequest,
  WhitelabelSettingsRequest,
} from '@api/dashboard/requests/whitelabel.request';
import { WhitelabelService } from '@api/dashboard/services/whitelabel.service';
import { catchError, distinctUntilChanged, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from '@services/toast.service';

interface Form {
  accent_color: FormControl<string | null>;
  accent_text_color: FormControl<string | null>;
  header_background_color: FormControl<string | null>;
  header_text_color: FormControl<string | null>;
  background_color: FormControl<string | null>;
  text_color: FormControl<string | null>;
  widget_background_color: FormControl<string | null>;
  widget_text_color: FormControl<string | null>;
}

@Component({
  selector: 'whitelabel-sidebar-tabs-styling',
  imports: [
    TranslocoDirective,
    InformationIconComponent,
    TranslocoPipe,
    ReactiveFormsModule,
    NgxColorsModule,
    KeyValuePipe,
    NgTemplateOutlet,
  ],
  templateUrl: './styling.component.html',
})
export class StylingComponent implements OnInit {
  public form = new FormGroup<Form>({
    accent_color: new FormControl(null),
    accent_text_color: new FormControl(null),
    header_background_color: new FormControl(null),
    header_text_color: new FormControl(null),
    background_color: new FormControl(null),
    text_color: new FormControl(null),
    widget_background_color: new FormControl(null),
    widget_text_color: new FormControl(null),
  });
  public loading = signal<boolean>(false);

  public whitelabelState = inject(WhitelabelState);
  private whitelabelService = inject(WhitelabelService);
  private destroyRef = inject(DestroyRef);
  private toastService = inject(ToastService);

  public ngOnInit(): void {
    this.initForm();
  }

  public submit(): void {
    if (this.loading() || this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.whitelabelState.submit(this.loading);
  }

  private initForm(): void {
    const whitelabel = this.whitelabelState.whitelabel();

    if (!whitelabel) {
      this.listenToForm();
      return;
    }

    this.form.patchValue({
      accent_color: whitelabel?.accent_color ?? null,
      accent_text_color: whitelabel?.accent_text_color ?? null,
      header_background_color: whitelabel?.header_background_color ?? null,
      header_text_color: whitelabel?.header_text_color ?? null,
      background_color: whitelabel?.background_color ?? null,
      text_color: whitelabel?.text_color ?? null,
      widget_background_color: whitelabel?.widget_background_color ?? null,
      widget_text_color: whitelabel?.widget_text_color ?? null,
    });

    this.listenToForm();
  }

  private listenToForm(): void {
    this.form.valueChanges
      .pipe(
        distinctUntilChanged(),
        tap((value) => {
          this.whitelabelState.whitelabel.update((whitelabel) =>
            whitelabel ? { ...whitelabel, ...value } : { ...value },
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
