<ng-container *transloco="let t; read: 'pages.whitelabel.sidebar.styling'">
  <div class="space-y-4 flex flex-col h-full pt-4">
    <div class="space-y-4 flex-1 overflow-y-auto px-6">
      <div class="flex items-center space-x-2">
        <h2 class="text-xl font-medium">{{ t('title') }}</h2>
        <information-icon [text]="t('information')" [customIcon]="true" width="w-64">
          <div customIcon class="bg-eaglo-blue h-4 w-4 rounded-full flex items-center justify-center cursor-pointer">
            <i class="fa-regular fa-info text-white text-[0.65rem]"></i>
          </div>
        </information-icon>
      </div>


      @for(control of form.controls | keyvalue; track $index) {
        <ng-container *ngTemplateOutlet="color; context: {control: control.value, translation: control.key.replaceAll('_', '-')} "></ng-container>
      }

    </div>
    <div class="flex justify-between items-center px-6 bg-white py-4">
      <a [href]="whitelabelState.portalUrl()" class="btn --outline">{{ 'general.buttons.back_to_portal' | transloco }}</a>
      <button (click)="submit()" [class.--loading]="loading()" class="btn --blue">{{ 'general.buttons.save' | transloco }}</button>
    </div>
  </div>

  <ng-template #color let-control="control" let-translation="translation">
    @if({open: false}; as state) {
      <div *transloco="let t; read: 'pages.whitelabel.sidebar.styling.' + translation" class="w-full bg-white rounded border-gray-300 border py-4 space-y-4">
        <div (click)="state.open = !state.open" class="flex justify-between items-center cursor-pointer px-4">
          <h2 class="text-lg select-none">{{ t('title') }}</h2>
          <div [class.rotate-180]="state.open" class="transition ease-in-out"><i class="fa-regular fa-chevron-down"></i></div>
        </div>

        @if(state.open && control) {
          <div class="w-full h-px bg-gray-300"></div>
          <div class="px-4">
            <div class="form-group-new">
              <label class="flex items-center space-x-2">
                <span>{{ t('color') }}</span>
                <information-icon [text]="t('description')" [customIcon]="true" width="w-64">
                  <div customIcon class="bg-eaglo-blue h-4 w-4 rounded-full flex items-center justify-center cursor-pointer">
                    <i class="fa-regular fa-info text-white text-[0.65rem]"></i>
                  </div>
                </information-icon>
              </label>
              <ngx-colors [formControl]="control" ngx-colors-trigger format="hex"></ngx-colors>
            </div>
          </div>
        }
      </div>
    }
  </ng-template>

</ng-container>
