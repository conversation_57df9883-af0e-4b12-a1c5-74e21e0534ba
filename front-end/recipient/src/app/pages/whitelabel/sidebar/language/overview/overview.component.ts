import {
  Component,
  DestroyRef,
  EventEmitter,
  inject,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { WhitelabelState } from '@states/whitelabel.state';
import { LanguageService } from '@services/language.service';
import { ConfigService } from '@app/services/config.service';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { KeyValuePipe } from '@angular/common';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';

@Component({
  selector: 'whitelabel-sidebar-language-overview',
  imports: [KeyValuePipe, TranslocoDirective, TranslocoPipe],
  templateUrl: './overview.component.html',
})
export class OverviewComponent implements OnInit {
  @Output() selectLanguage$: EventEmitter<string> = new EventEmitter();
  @Output() addLanguage$: EventEmitter<void> = new EventEmitter();
  public whitelabel = signal<Partial<Whitelabel> | null>(null);
  public languages = signal<{
    [key: string]: { language: string; country: string };
  }>({});
  public portalUrl = signal<string | null>(null);

  private whitelabelService = inject(WhitelabelState);
  private languageService = inject(LanguageService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelService.whitelabel;
    this.portalUrl = this.whitelabelService.portalUrl;

    this.loadLanguages();
  }

  public selectLanguage(language: string): void {
    this.selectLanguage$.emit(language);
  }

  public addLanguage(): void {
    this.addLanguage$.emit();
  }

  private loadLanguages(): void {
    this.languageService
      .load()
      .pipe(
        tap((languages) => {
          languages.forEach(
            (language) =>
              (this.languages()[language.code] = {
                language: language.language,
                country: language.country,
              }),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
