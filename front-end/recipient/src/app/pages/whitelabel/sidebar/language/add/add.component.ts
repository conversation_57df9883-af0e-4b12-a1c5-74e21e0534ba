import {
  Component,
  DestroyRef,
  EventEmitter,
  inject,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { Language } from '@interfaces/language.interface';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { LanguageService } from '@services/language.service';
import { WhitelabelState } from '@states/whitelabel.state';
import { tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgClass } from '@angular/common';
import { TranslocoPipe } from '@jsverse/transloco';

@Component({
  selector: 'whitelabel-sidebar-language-add',
  imports: [NgClass, ReactiveFormsModule, TranslocoPipe],
  templateUrl: './add.component.html',
})
export class AddComponent implements OnInit {
  @Output() selectLanguage$: EventEmitter<string> = new EventEmitter();
  @Output() cancel$: EventEmitter<void> = new EventEmitter();
  public languages = signal<Language[]>([]);
  public whitelabel = signal<Partial<Whitelabel> | null>(null);
  public control: FormControl<string> = new FormControl();

  private languageService = inject(LanguageService);
  private destroyRef = inject(DestroyRef);
  private whitelabelService = inject(WhitelabelState);

  public ngOnInit(): void {
    this.whitelabel = this.whitelabelService.whitelabel;
    this.loadLanguages();
  }

  public selectLanguage(): void {
    if (!this.control.value) {
      return;
    }

    this.selectLanguage$.emit(this.control.value);
  }

  public cancel(): void {
    this.cancel$.emit();
  }

  private loadLanguages(): void {
    this.languageService
      .load()
      .pipe(
        tap((response) => {
          this.languages.set(
            response
              .filter(
                (language) =>
                  (this.whitelabel()?.languages?.filter(
                    (activeLanguage) =>
                      activeLanguage.language === language.code,
                  ).length ?? 0) === 0,
              )
              .sort((a, b) => {
                if (a.language < b.language) {
                  return -1;
                }
                if (a.language > b.language) {
                  return 1;
                }

                return 0;
              }),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
