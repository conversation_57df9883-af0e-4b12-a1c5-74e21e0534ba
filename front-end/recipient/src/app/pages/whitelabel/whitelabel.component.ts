import { Component, inject, OnInit } from '@angular/core';
import { SidebarComponent } from '@pages/whitelabel/sidebar/sidebar.component';
import { DashboardComponent } from '@pages/dashboard/dashboard.component';
import { Section } from '@api/dashboard/models/section.interface';
import { Page } from '@api/dashboard/models/page.interface';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { AuthService } from '@services/auth.service';
import { Widget } from '@api/dashboard/models/widget.interface';
import { WidgetDataType } from '@api/dashboard/support/enums/widget-data-type.enum';

@Component({
  selector: 'app-whitelabel',
  imports: [SidebarComponent, DashboardComponent],
  templateUrl: './whitelabel.component.html',
})
export class WhitelabelComponent implements OnInit {
  private authService = inject(AuthService);

  public ngOnInit(): void {
    const dashboard: Dashboard = {
      slug: 'whitelabel',
      settings: null,
      pages: this.getPages(),
      account: {
        name: 'Whitela<PERSON>',
        about:
          '<h1>Lorem ipsum</h1><p></p><p><span style="color: rgb(29, 118, 219);">Lorem ipsum dolor sit amet. Sit internos labore qui accusantium dolor sit quibusdam commodi qui odio explicabo in nihil totam id quidem deserunt. Hic autem ipsa At maxime ratione id omnis sunt est eligendi sapiente. Sed vitae temporibus est laudantium quod aut velit beatae ab eligendi fuga qui enim autem. Eum repudiandae dolorem et tenetur laboriosam ut consequatur corporis in alias debitis et quae veritatis.</span></p><p></p><p>Et error sunt aut recusandae fuga in voluptas placeat et earum accusamus. Ad repudiandae molestias aut esse nihil eos culpa internos a minima tempora est incidunt nulla ut incidunt veniam?</p><p>Et molestiae facilis eum similique doloribus et minus voluptatum aut placeat cumque ut sequi quia et ipsam incidunt. Qui fugit odio ea expedita possimus et necessitatibus tempore. Et eligendi aperiam est amet suscipit et possimus cupiditate rem vero reiciendis sit mollitia assumenda qui quod fugit? Est eveniet sint ad consequuntur temporibus aut iure odio.</p><p>Vel illum odio ut necessitatibus autem ut laborum consectetur et culpa deleniti aut voluptates minima quo ipsa doloribus. Ex perspiciatis architecto ut aperiam aliquid sit tenetur provident sit consequuntur molestiae qui quos illo hic eveniet enim ut voluptatem odit. Et natus ipsa 33 suscipit sequi et autem similique et expedita inventore. At vitae minima ut placeat cumque et nobis eius vel nisi sint.</p><p></p><p>Aut incidunt rerum est odio rerum aut culpa beatae aut doloribus cumque eum cumque Quis a assumenda facilis. Hic voluptate voluptas sit rerum quisquam sit consequuntur dolorum. Aut dolores maiores vel molestiae tempora qui consequatur corporis est sapiente quae.</p>',
      },
      user: {
        firstname: 'Whitelabel',
        lastname: 'Editor',
        image_url: null,
      },
    };
    //
    this.authService.dashboard.set(dashboard);
  }

  private getPages(): Page[] {
    return [
      {
        id: 1,
        name: 'Executive',
        sections: this.getSections('executive'),
      },
      {
        id: 2,
        name: 'Trends',
        sections: this.getSections('trends'),
      },
    ];
  }

  private getSections(page: string): Section[] {
    switch (page) {
      case 'executive':
        return [
          {
            id: 1,
            name: 'Executive summary',
            grid_columns: 2,
            widgets: this.getWidgets('Executive summary'),
            tooltip:
              'Lorem ipsum dolor sit amet. Ut commodi iusto sit officiis delectus et amet officiis et numquam cupiditate et illum doloribus ea eius enim eum vitae repellendus? Est explicabo voluptates cum fugit culpa qui odit eligendi qui quisquam voluptas ad quidem quidem. Et aperiam amet aut ratione animi aut omnis nemo ut sapiente doloribus nam autem animi in magni ipsa. Qui illum adipisci ea libero rerum qui odit repudiandae aut nihil laboriosam sit ratione dolorem nam voluptates dolor.',
          },
          {
            id: 2,
            name: 'Executive overview',
            grid_columns: 4,
            widgets: this.getWidgets('Executive overview'),
            tooltip: null,
          },
        ];
      case 'trends':
        return [
          {
            id: 310,
            name: 'Auction Insights',
            grid_columns: 2,
            widgets: this.getWidgets('auction_insights'),
            tooltip: null,
          },
        ];
    }

    return [];
  }

  private getWidgets(section: string): Widget[] {
    switch (section) {
      case 'Executive summary':
        return [
          {
            id: 1,
            data_type: WidgetDataType.SEMI_CIRCULAR_CHART,
            name: 'Total conversion value',
            settings: {
              angle: {
                start: -90,
                end: 90,
              },
            },
            col_span: 1,
          },
          {
            id: 2,
            data_type: WidgetDataType.SEMI_CIRCULAR_CHART,
            name: 'ROAS',
            settings: {
              angle: {
                start: -90,
                end: 90,
              },
            },
            col_span: 1,
          },
        ];
      case 'Executive overview':
        return [
          {
            id: 3,
            data_type: WidgetDataType.DATA_COMPARISON,
            name: 'Impressions',
            settings: {
              icon: 'fa-regular fa-eye',
            },
            col_span: 1,
          },
          {
            id: 4,
            data_type: WidgetDataType.DATA_COMPARISON,
            name: 'Clicks',
            settings: {
              icon: 'fa-regular fa-arrow-pointer',
            },
            col_span: 1,
          },
          {
            id: 5,
            data_type: WidgetDataType.DATA_COMPARISON,
            name: 'Conversions',
            settings: {
              icon: 'fa-regular fa-cart-shopping',
            },
            col_span: 1,
          },
          {
            id: 6,
            data_type: WidgetDataType.DATA_COMPARISON,
            name: 'Revenue',
            settings: {
              prefix: '€',
              icon: 'fa-regular fa-dollar-sign',
            },
            col_span: 1,
          },
        ];
      case 'auction_insights':
        return [
          {
            id: 7,
            data_type: WidgetDataType.LINE_CHART,
            name: 'Impression Share',
            settings: {
              metric: 'impression_share',
            },
            col_span: 1,
          },
          {
            id: 8,
            data_type: WidgetDataType.PIE_CHART,
            name: 'Ad Relevance - Keyword count',
            settings: {
              factor: 'Ad relevance',
              metric: 'Keywordcount',
            },
            col_span: 1,
          },
        ];
    }

    return [];
  }
}
