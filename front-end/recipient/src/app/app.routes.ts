import { Routes } from '@angular/router';
import { authenticatedDashboardResolver } from './resolvers/authenticatedDashboardResolver';
import { DashboardComponent } from '@pages/dashboard/dashboard.component';
import { whitelabelResolver } from '@app/resolvers/whitelabelResolver';
import { WhitelabelComponent } from '@pages/whitelabel/whitelabel.component';

export const routes: Routes = [
  {
    path: 'error',
    loadComponent: () =>
      import('./pages/error/error.component').then((c) => c.ErrorComponent),
  },
  {
    path: 'whitelabel',
    resolve: {
      whitelabel: whitelabelResolver,
    },
    component: WhitelabelComponent,
    loadChildren: () =>
      import('./pages/dashboard/dashboard.routes').then((r) => r.routes),
  },
  {
    path: ':slug',
    resolve: {
      me: authenticatedDashboardResolver,
      whitelabel: whitelabelResolver,
    },
    component: DashboardComponent,
    loadChildren: () =>
      import('./pages/dashboard/dashboard.routes').then((r) => r.routes),
  },
  { path: '**', redirectTo: 'error' },
];
