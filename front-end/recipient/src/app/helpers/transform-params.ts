import { HttpParams } from '@angular/common/http';

export const paramsToHttpParams = (
  parameters: any,
  implodedArray: boolean = false,
): HttpParams => {
  let params = new HttpParams({});

  Object.keys(parameters).forEach((parameter) => {
    if (typeof parameters[parameter] === 'boolean') {
      params = params.append(parameter, parameters[parameter] ? 1 : 0);
      return;
    }

    if (Array.isArray(parameters[parameter])) {
      if (implodedArray) {
        params = params.append(parameter, parameters[parameter].join(','));
        return;
      }

      parameters[parameter].forEach((value: any) => {
        params = params.append(`${parameter.toString()}[]`, value);
      });
      return;
    }

    if (parameters[parameter] === null) {
      params = params.append(parameter, '');
      return;
    }

    if (parameters[parameter] === undefined) {
      params = params.append(parameter, '');
      return;
    }

    if (typeof parameters[parameter] === 'object') {
      params = params.append(parameter, JSON.stringify(parameters[parameter]));
      return;
    }

    params = params.append(parameter, parameters[parameter]);
  });

  return params;
};
