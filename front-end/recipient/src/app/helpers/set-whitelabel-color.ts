import { Renderer2 } from '@angular/core';

export const setWhitelabelColor = (
  renderer: Renderer2,
  document: Document,
  // whitelabel?: Whitelabel,
): void => {
  // if (!whitelabel || !whitelabel.accent_color) {
  //   return;
  // }
  // const style = renderer.createElement('style');
  // const text = renderer.createText(`
  //     .--whitelabel {
  //       background-color: ${whitelabel.accent_color};
  //       border-color: ${whitelabel.accent_color};
  //     }
  //     .--whitelabel-text {
  //       color: ${whitelabel.accent_color};
  //     }
  //     input[type="checkbox"].--whitelabel:checked {
  //       background-color: ${whitelabel.accent_color}1A !important;
  //       border-color: ${whitelabel.accent_color} !important;
  //     }
  //     .--whitelabel::after {
  //       border-color: ${whitelabel.accent_color} !important;
  //     }
  //     .--whitelabel:checked::before {
  //       background-color: ${whitelabel.accent_color} !important;
  //     }
  //     .--whitelabel-surface-text {
  //       color: ${whitelabel.surface_text_color}
  //     }
  //   `);
  // renderer.appendChild(style, text);
  // renderer.appendChild(document.head, style);
};
