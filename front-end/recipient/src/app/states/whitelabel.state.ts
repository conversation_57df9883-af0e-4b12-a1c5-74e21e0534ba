import {
  DestroyRef,
  inject,
  Injectable,
  signal,
  WritableSignal,
} from '@angular/core';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import {
  WhitelabelRequest,
  WhitelabelSettingsRequest,
} from '@api/dashboard/requests/whitelabel.request';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from '@services/toast.service';
import { WhitelabelService } from '@api/dashboard/services/whitelabel.service';

@Injectable({
  providedIn: 'root',
})
export class WhitelabelState {
  public whitelabel = signal<Partial<Whitelabel> | null>(null);
  public activeLanguage = signal<string | null>(null);
  public portalUrl = signal<string | null>(null);

  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private whitelabelService = inject(WhitelabelService);

  public submit(
    loading: WritableSignal<boolean>,
    customSettings: Partial<Whitelabel> = {},
  ): void {
    const body: WhitelabelRequest = {};

    const whitelabel = this.whitelabel();

    if (whitelabel) {
      body.settings = { ...whitelabel, ...customSettings };
    }

    if (whitelabel?.languages) {
      body.languages = whitelabel.languages;
    }

    loading.set(true);

    this.whitelabelService
      .update(body)
      .pipe(
        tap((response) => {
          this.whitelabel.set(response.data);
          loading.set(false);
          this.toastService.success();
        }),
        catchError((err) => {
          this.toastService.error();
          loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
