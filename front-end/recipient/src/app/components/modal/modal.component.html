<div class="relative" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="z-index: 2000">

  <div class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex min-h-full items-end justify-center text-center sm:items-center sm:p-0">
      <div
        [ngClass]="{
          'sm:!max-w-2xl': large,
          'sm:!max-w-4xl': extraLarge,
          '!p-0': !needsPadding
        }"
        class="relative transform rounded bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg p-4 sm:p-6">
        <div
          [ngClass]="{
            '!p-0': needsPadding
          }"
          class="flex justify-end p-4">
          <button (click)="close()" type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 cursor-pointer focus:outline-none">
            <i class="fa-light fa-xmark text-2xl px-2"></i>
          </button>
        </div>
        <ng-content></ng-content>
      </div>
    </div>
  </div>
</div>
