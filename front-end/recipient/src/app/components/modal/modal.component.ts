import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'components-modal',
  imports: [CommonModule],
  templateUrl: './modal.component.html',
})
export class ModalComponent {
  @Input() large: boolean = false;
  @Input() extraLarge: boolean = false;
  @Input() needsPadding: boolean = true;
  @Output() close$: EventEmitter<void> = new EventEmitter<void>();

  public close(): void {
    this.close$.emit();
  }
}
