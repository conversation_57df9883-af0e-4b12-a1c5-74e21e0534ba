<div
  (click)="toggleOpen(); $event.stopPropagation();"
  (mouseenter)="toggleOpen(true); $event.stopPropagation()"
  (mouseleave)="toggleOpen(true); $event.stopPropagation()"
  [class.cursor-pointer]="!disabled"
  class="relative"
>
  <div
    [ngClass]="{
      'flex items-center justify-center': needsFlex
    }"
    class="min-w-6 min-h-6">
    <div #icon>
      <ng-content select="[dropdownIcon]"></ng-content>

      @if(!customIcon) {
        <i class="fa-solid fa-ellipsis-vertical"></i>
      }
    </div>
  </div>

  <div
    #container
    [ngClass]="{
      'opacity-0 scale-95 invisible': !open,
      'opacity-100 scale-100': open,
      }"
    [class]="'flex flex-col bg-white p-2 rounded-lg drop-shadow-lg space-y-1 transition ease-in-out duration-200 -left-[8.5rem] z-50 fixed ' + width"
  >
    <ng-content></ng-content>
  </div>
</div>
