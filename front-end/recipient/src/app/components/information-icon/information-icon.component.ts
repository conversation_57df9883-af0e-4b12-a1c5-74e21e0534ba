import { Component, Input } from '@angular/core';
import { NgClass } from '@angular/common';

@Component({
  selector: 'information-icon',
  standalone: true,
  imports: [NgClass],
  templateUrl: './information-icon.component.html',
})
export class InformationIconComponent {
  @Input({ required: true }) text: string | undefined;
  @Input() floatLeft: boolean = false;
  @Input() customIcon: boolean = false;
  @Input() width: string = 'w-96';
}
