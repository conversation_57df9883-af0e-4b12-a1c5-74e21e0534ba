import {
  ChangeDetectorRef,
  Component,
  forwardRef,
  inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { SelectComponent } from '@components/form-inputs/select/select.component';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { catchError, Observable, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { SelectOption } from '@app/interfaces/select-option.interface';

@Component({
  selector: 'form-connected-select',
  standalone: true,
  imports: [
    CommonModule,
    TranslocoPipe,
    ReactiveFormsModule,
    TranslocoDirective,
  ],
  templateUrl: '../select/select.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ConnectedSelectComponent),
      multi: true,
    },
  ],
})
export class ConnectedSelectComponent
  extends SelectComponent
  implements OnInit, OnChanges
{
  // Inputs
  @Input({ required: true }) source!: (
    page: number,
    filters: { [key: string]: any },
    search?: string | null,
  ) => Observable<PaginatedResponse<SelectOption<any>>>;
  @Input() filters: { [key: string]: any } = {};

  // State
  protected lastPage?: number;
  protected currentPage?: number;

  public override ngOnInit(): void {
    super.ngOnInit();

    this.loadData();
    this.listenToNextPage();
    this.listenToNewSearch();
  }

  public override ngOnChanges(changes: SimpleChanges) {
    super.ngOnChanges(changes);

    if ('source' in changes && !changes['source'].firstChange) {
      this.currentPage = undefined;
      this.lastPage = undefined;
      this.data = [];
      this.loadData();
    }
  }

  public listenToNextPage(): void {
    this.nextPage.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.loadData();
    });
  }

  public listenToNewSearch(): void {
    this.newSearchValue
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.currentPage = undefined;
        this.lastPage = undefined;
        this.data = [];

        this.loadData();
      });
  }

  public loadData(): void {
    if (
      this.lastPage !== undefined &&
      this.currentPage !== undefined &&
      this.lastPage === this.currentPage
    ) {
      this.isLoading = false;
      this.activateScrollListener();

      return;
    }

    this.isLoading = true;
    this.changeDetectorRef.detectChanges();

    this.source(
      this.currentPage ? this.currentPage + 1 : 1,
      this.filters,
      this.searchValue.value,
    )
      .pipe(
        tap((response) => {
          this.data = [...this.data, ...response.data];
          this.currentPage = response.meta.current_page;
          this.lastPage = response.meta.last_page;
          this.setSelectedValuesFromData(this.selectedValues);
          this.isLoading = false;
          this.activateScrollListener();
          this.changeDetectorRef.detectChanges();
        }),
        catchError((err) => {
          this.isLoading = false;
          this.activateScrollListener();
          this.changeDetectorRef.detectChanges();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
