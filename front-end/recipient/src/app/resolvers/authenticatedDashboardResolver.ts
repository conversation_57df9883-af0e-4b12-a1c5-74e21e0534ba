import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from '@services/auth.service';
import { catchError, combineLatest, Observable, of } from 'rxjs';
import { AuthenticationService } from '@api/auth/services/authentication.service';

export const authenticatedDashboardResolver: ResolveFn<Observable<any>> = (
  route,
  state,
) => {
  const authService = inject(AuthService);
  const authenticationService = inject(AuthenticationService);
  const router = inject(Router);

  return combineLatest([
    authenticationService.token().pipe(catchError(() => of(true))),
    authService.loadDashboard(
      route.params['slug'],
      route.queryParams['signature'],
    ),
  ]).pipe(
    catchError((err) => {
      router.navigate(['error']);
      return of(false);
    }),
  );
};
