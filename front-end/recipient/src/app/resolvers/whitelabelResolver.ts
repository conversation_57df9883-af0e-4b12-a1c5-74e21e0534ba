import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from '@services/auth.service';
import { catchError, combineLatest, Observable, of, tap } from 'rxjs';
import { AuthenticationService } from '@api/auth/services/authentication.service';
import { WhitelabelService as ApiService } from '@api/dashboard/services/whitelabel.service';
import { DataResponse } from '@api/support/responses/data.response';
import { Whitelabel } from '@api/dashboard/models/whitelabel.interface';
import { HttpErrorResponse } from '@angular/common/http';
import { WhitelabelService } from '@services/whitelabel.service';

export const whitelabelResolver: ResolveFn<
  Observable<DataResponse<Whitelabel> | null>
> = (route, state) => {
  const apiService = inject(ApiService);
  const whitelabelService = inject(WhitelabelService);
  const router = inject(Router);

  return apiService.show().pipe(
    tap((response) => {
      whitelabelService.setWhitelabel(response.data);
    }),
    catchError((err: HttpErrorResponse) => {
      if (err.status === 401) {
        router.navigate(['error']);
      }
      return of(null);
    }),
  );
};
