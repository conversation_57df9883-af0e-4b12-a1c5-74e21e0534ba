import { inject, Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'safeHtml',
})
export class SafeHtmlPipe implements PipeTransform {
  private sanitizer = inject(DomSanitizer);

  public transform(html: string | null | undefined): SafeHtml | null {
    if (!html) {
      return null;
    }

    return this.sanitizer.bypassSecurityTrustHtml(html);
  }
}
