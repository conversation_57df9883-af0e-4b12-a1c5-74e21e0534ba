/* You can add global styles to this file, and also import other style files */
@import "tailwindcss";
@import './inputs.scss';
@import './buttons.scss';
@import './hot-toast.scss';
@config "../../tailwind.config.js";
@plugin "@tailwindcss/typography";

html,
body {
  @apply h-full w-full text-base;
}

body {
  @apply text-eaglo-gray-700;
}

.preview {
  @apply w-full! rounded!;

  .preview-background {
    @apply rounded!;

    .circle {
      @apply rounded! w-full!;
    }
  }
}

.blue {
  @apply text-blue-500! underline!;
}


.inner-html-container,.NgxEditor__Wrapper {
  @apply whitespace-pre-wrap;

  & p:empty {
    min-height: 1em;
    display: block;
  }

  & ul {
    list-style-type: disc !important;
    padding-left: 2rem !important;
    margin: 1rem 0 !important;
  }

  & ol {
    list-style-type: decimal !important;
    padding-left: 2rem !important;
    margin: 1rem 0 !important;
  }

  & li {
    display: list-item !important;
  }

  h1 {
    @apply text-4xl;
  }
  h2 {
    @apply text-3xl;
  }
  h3 {
    @apply text-2xl
  }
  h4 {
    @apply text-xl;
  }
  h5 {
    @apply text-lg;
  }
  h6 {
    @apply text-base;
  }
}
