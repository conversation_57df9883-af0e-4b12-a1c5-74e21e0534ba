/* prettier-ignore */

.link {
  @apply underline underline-offset-2 cursor-pointer;
}

.hover-link {
  @apply hover:underline underline-offset-2 cursor-pointer;
}


.btn {
  @apply px-6 py-2 rounded relative flex items-center justify-center space-x-2 shadow-black transition ease-in-out bg-eaglo-blue text-white border border-eaglo-blue  hover:-translate-y-[2px] hover:scale-[1.02] hover:drop-shadow-lg cursor-pointer;

  * {
    @apply text-white;
  }

  &.__pill {
    @apply rounded-full;
  }

  &.--black {
    @apply bg-black text-white border-black
  }

  &.--danger {
    @apply bg-red-500 text-white border-red-500;
  }

  &.--round {
    @apply px-4 py-2 rounded-full;
  }

  &.--bordered {
    @apply border-gray-400 border text-gray-700 transition;
  }

  &.--success {
    @apply bg-green-500 text-white border-green-500;
  }

  &.--small {
    @apply px-4 py-1 text-base;
  }

  &.--edit {
    @apply bg-orange-500 text-white border-orange-500;
  }

  &.--bordered-blue {
    @apply border-eaglo-blue border text-eaglo-blue hover:ring-1 ring-eaglo-blue transition
  }

  &.--disabled {
    @apply bg-gray-400! text-white cursor-not-allowed hover:-translate-y-0 hover:scale-100 hover:shadow-none transition-none border-gray-400;
  }

  &.--outline {
    @apply text-eaglo-gray border-eaglo-gray border bg-white;

    * {
      @apply text-eaglo-gray;
    }
  }

  &.--loading {
    @apply bg-gray-200/60 text-transparent relative cursor-not-allowed;

    &::before {
      content: "";
      position: absolute;
      width: 1.5em;
      height: 1.5em;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      border: 4px solid transparent;
      border-radius: 50%;
      animation: btn-loading-spinner 1s ease infinite;
      @apply border-t-eaglo-blue
    }
  }

  &>.tooltip {
    @apply absolute bg-white text-eaglo-gray rounded-lg shadow-xl px-1 py-2 z-50 top-auto bottom-14 opacity-0 transition pointer-events-none;

    &::before {
      content: "";
      @apply w-4 h-4 bg-white block absolute -bottom-2 left-[45.8%] rotate-45
    }
  }

  &:hover {
    &>.tooltip {
      @apply opacity-100
    }
  }
}

button.btn {
  @apply hover:-translate-y-[2px] hover:scale-[1.02] hover:drop-shadow-lg;
}

.btn-dropdown {
  @apply flex items-center space-x-3 hover:bg-eaglo-gray/10 p-2 rounded-lg cursor-pointer;
}

@keyframes btn-loading-spinner {
  from {
    transform: rotate(0turn);
  }

  to {
    transform: rotate(1turn);
  }
}

.gsi-material-button {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -webkit-appearance: none;
  background-color: WHITE;
  background-image: none;
  border: 1px solid #747775;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #1f1f1f;
  cursor: pointer;
  font-family: 'Roboto', arial, sans-serif;
  font-size: 14px;
  height: 40px;
  letter-spacing: 0.25px;
  outline: none;
  overflow: hidden;
  padding: 0 12px;
  position: relative;
  text-align: center;
  -webkit-transition: background-color .218s, border-color .218s, box-shadow .218s;
  transition: background-color .218s, border-color .218s, box-shadow .218s;
  vertical-align: middle;
  white-space: nowrap;
  width: auto;
  max-width: 400px;
  min-width: min-content;
}

.gsi-material-button .gsi-material-button-icon {
  height: 20px;
  margin-right: 12px;
  min-width: 20px;
  width: 20px;
}

.gsi-material-button .gsi-material-button-content-wrapper {
  -webkit-align-items: center;
  align-items: center;
  display: flex;
  -webkit-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  flex-wrap: nowrap;
  height: 100%;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.gsi-material-button .gsi-material-button-contents {
  -webkit-flex-grow: 1;
  flex-grow: 1;
  font-family: 'Roboto', arial, sans-serif;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
}

.gsi-material-button .gsi-material-button-state {
  -webkit-transition: opacity .218s;
  transition: opacity .218s;
  bottom: 0;
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.gsi-material-button:disabled {
  cursor: default;
  background-color: #ffffff61;
  border-color: #1f1f1f1f;
}

.gsi-material-button:disabled .gsi-material-button-contents {
  opacity: 38%;
}

.gsi-material-button:disabled .gsi-material-button-icon {
  opacity: 38%;
}

.gsi-material-button:not(:disabled):active .gsi-material-button-state,
.gsi-material-button:not(:disabled):focus .gsi-material-button-state {
  background-color: #303030;
  opacity: 12%;
}

.gsi-material-button:not(:disabled):hover {
  -webkit-box-shadow: 0 1px 2px 0 rgba(60, 64, 67, .30), 0 1px 3px 1px rgba(60, 64, 67, .15);
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, .30), 0 1px 3px 1px rgba(60, 64, 67, .15);
}

.gsi-material-button:not(:disabled):hover .gsi-material-button-state {
  background-color: #303030;
  opacity: 8%;
}
