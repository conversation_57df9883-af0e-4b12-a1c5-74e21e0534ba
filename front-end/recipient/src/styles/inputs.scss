.ng-submitted {
  .input {
    &.ng-invalid {
      @apply border-red-500!;
    }
  }

  .form-group:has(.ng-invalid), .form-group-new:has(.ng-invalid) {
    input {
      @apply border-red-500! outline-red-500!;
    }

    label {
      @apply text-red-500!;
    }

    textarea {
      @apply border-red-500! outline-red-500!;
    }

    select {
      @apply border-red-500! outline-red-500!;
    }
  }
}

.input {
  @apply w-full px-4! py-2! border! border-gray-300! rounded! font-light! bg-white! ring-gray-400! outline-gray-400! focus:ring-0! focus:outline-1! outline-offset-0! mt-2!
}

.form-group {
  @apply relative w-full;

  >input,
  select,
  textarea,
  nz-date-picker {
    @apply w-full px-4! py-2! border! border-gray-300! rounded! font-light! bg-white! ring-gray-400! outline-gray-400! focus:ring-0! focus:outline-1! outline-offset-0!;
  }

  label {
    z-index: 2;
    @apply text-sm absolute -top-2.5 left-3 px-1 text-eaglo-gray bg-transparent;
  }

  label::before {
    content: "";
    z-index: -1;
    @apply absolute left-0 right-0 bottom-0.5 h-1/2 bg-white;
  }


  .icon {
    @apply absolute right-4 text-xl top-1.5 bg-white;
  }
}

.form-group-new {
  @apply relative w-full;

  >input,
  select,
  textarea,
  nz-date-picker {
    @apply w-full px-4! py-2! border! border-gray-300! rounded! font-light! bg-white! ring-gray-400! outline-gray-400! focus:ring-0! focus:outline-1! outline-offset-0! mt-2! placeholder:text-gray-400!;
  }

  .icon {
    @apply absolute right-4 text-xl top-1.5 bg-white;
  }
}

.form-group:not(.--without-disable-state) {
  select:disabled {
    &+label {
      @apply text-gray-400
    }

    &~.icon {
      @apply text-gray-400;
    }
  }

  input:disabled {
    @apply text-gray-400;

    &+label {
      @apply text-gray-400;
    }

    &~.icon {
      @apply text-gray-400;
    }
  }

  textarea:disabled {
    &+label {
      @apply text-gray-400
    }

    &~.icon {
      @apply text-gray-400;
    }
  }
}

.form-group:has(.icon) {

  >input,
  select,
  textarea,
  nz-date-picker {
    @apply pr-8! truncate!;
  }
}


.checkbox {
  @apply flex space-x-2 items-center;

  input {
    @apply h-4 w-4 rounded border-gray-300 text-eaglo-blue outline-0 focus:ring-0 cursor-pointer;
  }

  label {
    @apply cursor-pointer select-none;
  }

}

.toggle {
  @apply flex items-center space-x-2;

  input {
    @apply border-none outline-0 relative h-5 w-10 cursor-pointer;

    &::before {
      content: '';
      @apply h-5 w-10 rounded-full bg-gray-400 block -left-1 absolute transition ease-in-out;
    }

    &::after {
      content: '';
      @apply h-3.5 w-3.5 bg-white rounded-full absolute top-[3px] transition-transform ease-in-out;
    }

    &:checked {
      &::before {
        @apply bg-eaglo-blue;
      }

      &::after {
        @apply translate-x-[18px];
      }
    }
  }

  label {
    @apply text-eaglo-gray cursor-pointer;
  }
}

.toggle-connection {
  @apply flex items-center space-x-2;

  input {
    @apply border-none outline-0 relative h-7 w-[3.65rem] cursor-pointer;

    &::before {
      content: '';
      @apply h-7 w-16 rounded-full bg-gray-300 block -left-1 absolute transition ease-in-out;
    }

    &::after {
      content: '';
      @apply h-[1.40rem] w-[1.40rem] bg-white rounded-full absolute top-[3px] transition-transform ease-in-out;
    }

    &:checked {
      &::before {
        @apply bg-eaglo-blue;
      }

      &::after {
        @apply translate-x-[34px];
      }
    }
  }
}


.form-group-new:not(.--without-disable-state) {
  select:disabled {
    @apply bg-gray-400/10! cursor-not-allowed!;

    &~.icon {
      @apply text-gray-400;
    }
  }

  input:disabled {
    @apply bg-gray-400/10! cursor-not-allowed!;

    &~.icon {
      @apply text-gray-400;
    }
  }

  textarea:disabled {
    @apply bg-gray-400/10! cursor-not-allowed!;

    &~.icon {
      @apply text-gray-400;
    }
  }
}

.NgxEditor {
  @apply overflow-y-auto;

  p {
    @apply m-0!;
  }
}
